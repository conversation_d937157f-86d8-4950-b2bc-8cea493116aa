import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/models/holiday_model.dart';
import '../../../../core/utils/time_utils.dart';
import 'time_slot_selection_dialog.dart';
import '../../../appointments/data/repositories/appointments_repository.dart';
import '../../../appointments/data/repositories/time_slots_repository.dart';
import '../../../appointments/data/repositories/holidays_repository.dart';
import '../pages/print_appointment_invoice_page.dart';

class MultipleAppointmentsWidget extends StatefulWidget {
  final PatientModel patient;
  final VoidCallback? onPatientReset;

  const MultipleAppointmentsWidget({
    super.key,
    required this.patient,
    this.onPatientReset,
  });

  @override
  State<MultipleAppointmentsWidget> createState() => _MultipleAppointmentsWidgetState();
}

class _MultipleAppointmentsWidgetState extends State<MultipleAppointmentsWidget> {
  final AppointmentsRepository _appointmentsRepository = AppointmentsRepository();
  final TimeSlotsRepository _timeSlotsRepository = TimeSlotsRepository();
  final HolidaysRepository _holidaysRepository = HolidaysRepository();

  final TextEditingController _numberOfAppointmentsController = TextEditingController();
  final TextEditingController _totalFeeController = TextEditingController();
  final TextEditingController _paidAmountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  int _numberOfAppointments = 1;
  List<DateTime?> _selectedDates = [];
  List<TimeSlotModel?> _selectedTimeSlots = [];
  List<List<TimeSlotModel>> _availableTimeSlotsForEachDate = [];
  List<bool> _isLoadingTimeSlots = [];

  @override
  void initState() {
    super.initState();
    _numberOfAppointmentsController.text = _numberOfAppointments.toString();
    _setNumberOfAppointments();
  }

  @override
  void dispose() {
    _numberOfAppointmentsController.dispose();
    _totalFeeController.dispose();
    _paidAmountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _setNumberOfAppointments() {
    if (_numberOfAppointments > 0 && _numberOfAppointments <= 50) {
      setState(() {
        _selectedDates = List.filled(_numberOfAppointments, null);
        _selectedTimeSlots = List.filled(_numberOfAppointments, null);
        _availableTimeSlotsForEachDate = List.generate(_numberOfAppointments, (index) => <TimeSlotModel>[]);
        _isLoadingTimeSlots = List.filled(_numberOfAppointments, false);
      });
    }
  }

  Future<void> _selectDate(int index) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // Check if the selected date is a holiday
      final holidays = await _checkHolidaysForDate(picked);
      if (holidays.isNotEmpty) {
        if (mounted) {
          _showHolidayDialog(picked, holidays, index);
        }
        return;
      }

      setState(() {
        _selectedDates[index] = picked;
        _selectedTimeSlots[index] = null;
        _availableTimeSlotsForEachDate[index] = [];
      });
      
      await _loadAvailableTimeSlots(index);
    }
  }

  Future<List<HolidayModel>> _checkHolidaysForDate(DateTime date) async {
    try {
      final holidays = await _holidaysRepository.getHolidaysByDate(date);
      return holidays;
    } catch (e) {
      debugPrint('Error checking holidays: $e');
      return [];
    }
  }

  void _resetFormWithoutNavigation() {
    setState(() {
      _numberOfAppointments = 1;
      _numberOfAppointmentsController.text = '1';
      _selectedDates = [];
      _selectedTimeSlots = [];
      _availableTimeSlotsForEachDate = [];
      _isLoadingTimeSlots = [];
      _totalFeeController.clear();
      _paidAmountController.clear();
      _notesController.clear();
    });
    _setNumberOfAppointments();
  }



  Future<void> _showTimeSlotSelectionDialog(int index) async {
    if (_selectedDates[index] == null || _availableTimeSlotsForEachDate[index].isEmpty) return;

    final selectedTimeSlot = await showDialog<TimeSlotModel>(
      context: context,
      builder: (context) => TimeSlotSelectionDialog(
        availableTimeSlots: _availableTimeSlotsForEachDate[index],
        selectedTimeSlot: _selectedTimeSlots[index],
        selectedDate: _selectedDates[index]!,
      ),
    );

    if (selectedTimeSlot != null) {
      setState(() {
        _selectedTimeSlots[index] = selectedTimeSlot;
      });
    }
  }

  void _showHolidayDialog(DateTime date, List<HolidayModel> holidays, int appointmentIndex) {
    final dayName = TimeUtils.getArabicDayName(date.weekday);
    final dateStr = '${date.day}/${date.month}/${date.year}';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.event_busy, color: AppColors.error, size: 24.sp),
            SizedBox(width: 8.w),
            Text(
              'يوم إجازة - الموعد ${appointmentIndex + 1}',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.error,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$dayName - $dateStr',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              'هذا التاريخ يوافق إجازة رسمية:',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 8.h),
            ...holidays.map((holiday) => Container(
              margin: EdgeInsets.only(bottom: 8.h),
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
              ),
              child: Text(
                holiday.occasionName,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.error,
                ),
              ),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'موافق',
              style: TextStyle(color: AppColors.primary),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _loadAvailableTimeSlots(int index) async {
    if (_selectedDates[index] == null) return;

    setState(() {
      _isLoadingTimeSlots[index] = true;
    });

    try {
      final timeSlots = await _timeSlotsRepository.getAvailableTimeSlots(_selectedDates[index]!);
      setState(() {
        _availableTimeSlotsForEachDate[index] = timeSlots;
        _isLoadingTimeSlots[index] = false;
      });
    } catch (e) {
      setState(() {
        _availableTimeSlotsForEachDate[index] = [];
        _isLoadingTimeSlots[index] = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الأوقات المتاحة للموعد ${index + 1}: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _bookMultipleAppointments() async {
    // Validation
    if (_numberOfAppointments == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى تحديد عدد المواعيد'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    for (int i = 0; i < _numberOfAppointments; i++) {
      if (_selectedDates[i] == null || _selectedTimeSlots[i] == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('يرجى اختيار التاريخ والوقت للموعد ${i + 1}'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
    }

    final totalFee = double.tryParse(_totalFeeController.text) ?? 0.0;
    final paidAmount = double.tryParse(_paidAmountController.text) ?? 0.0;

    try {
      final timeSlotIds = _selectedTimeSlots.map((slot) => slot!.id).toList();
      final dates = _selectedDates.map((date) => date!).toList();

      final createdAppointments = await _appointmentsRepository.createMultipleAppointments(
        patientId: widget.patient.id,
        timeSlotIds: timeSlotIds,
        appointmentDates: dates,
        totalConsultationFee: totalFee,
        totalPaidAmount: paidAmount,
        notes: _notesController.text.trim(),
      );

      if (mounted) {
        _showSuccessDialog(createdAppointments);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حجز المواعيد: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showSuccessDialog(List<AppointmentModel> appointments) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.success, size: 24.sp),
            SizedBox(width: 8.w),
            Text(
              'تم الحجز بنجاح',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
          ],
        ),
        content: Text(
          'تم حجز ${appointments.length} مواعيد بنجاح. هل تريد طباعة فواتير الحجوزات؟',
          style: TextStyle(fontSize: 14.sp),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              _resetFormWithoutNavigation(); // Reset form only
            },
            child: Text(
              'لا، شكراً',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              _printMultipleAppointments(appointments);
            },
            icon: Icon(Icons.print, size: 16.sp, color: AppColors.white),
            label: Text(
              'طباعة الفواتير',
              style: TextStyle(color: AppColors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  void _printMultipleAppointments(List<AppointmentModel> appointments) {
    // Just print the first appointment for now to avoid navigation issues
    if (appointments.isNotEmpty) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PrintAppointmentInvoicePage(
            appointment: appointments[0],
            patient: widget.patient,
          ),
        ),
      ).then((_) {
        if (mounted) {
          Navigator.of(context).pop(); // Close dialog
          widget.onPatientReset?.call(); // Reset patient in parent
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Number of Appointments
          _buildSectionTitle('عدد المواعيد'),
          SizedBox(height: 8.h),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'عدد الجلسات:',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Text(
                        '$_numberOfAppointments جلسة',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: AppColors.primary,
                    inactiveTrackColor: AppColors.primary.withValues(alpha: 0.3),
                    thumbColor: AppColors.primary,
                    overlayColor: AppColors.primary.withValues(alpha: 0.2),
                    valueIndicatorColor: AppColors.primary,
                    valueIndicatorTextStyle: TextStyle(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  child: Slider(
                    value: _numberOfAppointments.toDouble(),
                    min: 1,
                    max: 50,
                    divisions: 49,
                    label: '$_numberOfAppointments',
                    onChanged: (double value) {
                      setState(() {
                        _numberOfAppointments = value.round();
                        _numberOfAppointmentsController.text = _numberOfAppointments.toString();
                      });
                      _setNumberOfAppointments();
                    },
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '1',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      '50',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: 20.h),

          // Appointments List
          if (_numberOfAppointments > 0) ...[
            _buildSectionTitle('تفاصيل المواعيد'),
            SizedBox(height: 8.h),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _numberOfAppointments,
              itemBuilder: (context, index) {
                return _buildAppointmentCard(index);
              },
            ),

            SizedBox(height: 20.h),

            // Payment Information
            _buildSectionTitle('معلومات الدفع'),
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _totalFeeController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'إجمالي الرسوم *',
                      hintText: '0.00',
                      suffixText: 'ج.م',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      prefixIcon: Icon(Icons.attach_money, color: AppColors.primary),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال إجمالي الرسوم';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: TextFormField(
                    controller: _paidAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'المبلغ المدفوع *',
                      hintText: '0.00',
                      suffixText: 'ج.م',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      prefixIcon: Icon(Icons.payment, color: AppColors.success),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال المبلغ المدفوع';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),

            SizedBox(height: 12.h),

            // Notes
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),

            SizedBox(height: 20.h),

            // Book Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _bookMultipleAppointments,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: Text(
                  'حجز $_numberOfAppointments مواعيد',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
          ] else ...[
            // No appointments set
            SizedBox(
              height: 200.h,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.event_repeat,
                      size: 80.sp,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      'يرجى تحديد عدد المواعيد أولاً',
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAppointmentCard(int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الموعد ${index + 1}',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          SizedBox(height: 12.h),

          // Date Selection
          InkWell(
            onTap: () => _selectDate(index),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.primary.withValues(alpha: 0.5)),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  Icon(Icons.calendar_today, color: AppColors.primary, size: 20.sp),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      _selectedDates[index] == null
                          ? 'اختر التاريخ'
                          : '${TimeUtils.getArabicDayName(_selectedDates[index]!.weekday)} - ${_selectedDates[index]!.day}/${_selectedDates[index]!.month}/${_selectedDates[index]!.year}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: _selectedDates[index] == null
                            ? AppColors.textSecondary
                            : AppColors.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 12.h),

          // Time Slot Selection
          if (_selectedDates[index] != null) ...[
            if (_isLoadingTimeSlots[index])
              Center(
                child: CircularProgressIndicator(color: AppColors.primary, strokeWidth: 2),
              )
            else if (_availableTimeSlotsForEachDate[index].isEmpty)
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: AppColors.error.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  'لا توجد أوقات متاحة',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.error,
                  ),
                  textAlign: TextAlign.center,
                ),
              )
            else
              InkWell(
                onTap: () => _showTimeSlotSelectionDialog(index),
                borderRadius: BorderRadius.circular(8.r),
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: _selectedTimeSlots[index] != null
                          ? AppColors.primary
                          : AppColors.primary.withValues(alpha: 0.3),
                      width: _selectedTimeSlots[index] != null ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8.r),
                    color: _selectedTimeSlots[index] != null
                        ? AppColors.primary.withValues(alpha: 0.05)
                        : AppColors.white,
                  ),
                  child: _selectedTimeSlots[index] == null
                      ? Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(6.w),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(6.r),
                              ),
                              child: Icon(
                                Icons.access_time,
                                color: AppColors.primary,
                                size: 16.sp,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              'اختر الوقت',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColors.textSecondary,
                              ),
                            ),
                            const Spacer(),
                            Icon(
                              Icons.keyboard_arrow_down,
                              color: AppColors.primary,
                              size: 20.sp,
                            ),
                          ],
                        )
                      : Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(6.w),
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(6.r),
                              ),
                              child: Icon(
                                Icons.schedule,
                                color: AppColors.white,
                                size: 16.sp,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    TimeUtils.formatTimeRange(_selectedTimeSlots[index]!.startTime, _selectedTimeSlots[index]!.endTime),
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                  if (_selectedTimeSlots[index]!.employeeName != null)
                                    Text(
                                      'د. ${_selectedTimeSlots[index]!.employeeName}${_selectedTimeSlots[index]!.specialization != null ? ' - ${_selectedTimeSlots[index]!.specialization}' : ''}',
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            Icon(
                              Icons.edit,
                              color: AppColors.primary,
                              size: 16.sp,
                            ),
                          ],
                        ),
                ),
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }
}
