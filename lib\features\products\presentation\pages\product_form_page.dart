import 'dart:async';
import 'dart:io';
import 'package:iihc_admin/features/products/data/repositories/products_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/product_model.dart';
import '../../../../core/models/product_image_model.dart';
import '../../../../core/widgets/loading_dialog.dart';

import '../../../categories/presentation/bloc/categories_bloc.dart';
import '../../../categories/presentation/bloc/categories_event.dart';
import '../../../categories/presentation/bloc/categories_state.dart';
import '../bloc/products_bloc.dart';
import '../bloc/products_event.dart';
import '../bloc/products_state.dart';

class ProductFormPage extends StatefulWidget {
  final ProductModel? product;
  final bool isEditing;

  const ProductFormPage({
    super.key,
    this.product,
    this.isEditing = false,
  });

  @override
  State<ProductFormPage> createState() => _ProductFormPageState();
}

class _ProductFormPageState extends State<ProductFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _productCodeController = TextEditingController();

  final _originalPriceController = TextEditingController();
  final _sellingPriceController = TextEditingController();
  final _discountController = TextEditingController();
  final _stockController = TextEditingController();

  String? _selectedCategoryId;
  final List<XFile> _selectedImages = [];
  final ImagePicker _imagePicker = ImagePicker();
  bool _isActive = true;

  // Product code validation
  Timer? _debounceTimer;
  bool _isCheckingProductCode = false;
  String? _productCodeError;

  @override
  void initState() {
    super.initState();

    // Load categories
    context.read<CategoriesBloc>().add(LoadActiveCategories());

    // Initialize form if editing
    if (widget.isEditing && widget.product != null) {
      _nameController.text = widget.product!.name;
      _descriptionController.text = widget.product!.description;
      _productCodeController.text = widget.product!.productCode;

      _originalPriceController.text = widget.product!.originalPrice?.toString() ?? '';
      _sellingPriceController.text = widget.product!.sellingPrice?.toString() ?? '';
      _discountController.text = widget.product!.discountPercentage.toString();
      _stockController.text = widget.product!.stock.toString();
      _selectedCategoryId = widget.product!.categoryId;
      _isActive = widget.product!.isActive;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _productCodeController.dispose();

    _originalPriceController.dispose();
    _sellingPriceController.dispose();
    _discountController.dispose();
    _stockController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // Check if product code is unique
  Future<void> _checkProductCode(String productCode) async {
    if (productCode.isEmpty) {
      setState(() {
        _productCodeError = null;
        _isCheckingProductCode = false;
      });
      return;
    }

    // Cancel previous timer
    _debounceTimer?.cancel();

    // Set checking state
    setState(() {
      _isCheckingProductCode = true;
      _productCodeError = null;
    });

    // Start new timer
    _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
      try {
        // Use the repository directly
        final repository = ProductsRepository();

        // Check if code exists (excluding current product if editing)
        final existingProducts = await repository.searchProducts(productCode);
        final codeExists = existingProducts.any((product) =>
          product.productCode.toLowerCase() == productCode.toLowerCase() &&
          (widget.product == null || product.id != widget.product!.id)
        );

        if (mounted) {
          setState(() {
            _isCheckingProductCode = false;
            _productCodeError = codeExists ? 'كود المنتج مستخدم بالفعل' : null;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isCheckingProductCode = false;
            _productCodeError = null; // Don't show error for network issues
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ProductsBloc, ProductsState>(
      listener: (context, state) {
        if (state is ProductsError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ: ${state.message}'),
              backgroundColor: AppColors.error,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: Text(widget.isEditing ? 'تعديل المنتج' : 'إضافة منتج جديد'),
          backgroundColor: AppColors.white,
          elevation: 0,
          actions: [
            TextButton(
              onPressed: _saveProduct,
              child: Text(
                widget.isEditing ? 'تحديث' : 'حفظ',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                  fontSize: 16.sp,
                ),
              ),
            ),
          ],
        ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Name
              _buildSectionTitle('اسم المنتج *'),
              SizedBox(height: 8.h),
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  hintText: 'أدخل اسم المنتج',
                  prefixIcon: const Icon(Icons.inventory),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى إدخال اسم المنتج';
                  }
                  return null;
                },
              ),

              SizedBox(height: 20.h),

              // Product Code
              _buildSectionTitle('كود المنتج *'),
              SizedBox(height: 8.h),
              TextFormField(
                controller: _productCodeController,
                onChanged: _checkProductCode,
                decoration: InputDecoration(
                  hintText: 'أدخل كود المنتج (مثل: PROD-0001)',
                  prefixIcon: const Icon(Icons.qr_code),
                  suffixIcon: _isCheckingProductCode
                      ? SizedBox(
                          width: 20.w,
                          height: 20.h,
                          child: Padding(
                            padding: EdgeInsets.all(12.w),
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                            ),
                          ),
                        )
                      : _productCodeError != null
                          ? Icon(Icons.error, color: AppColors.error)
                          : _productCodeController.text.isNotEmpty
                              ? Icon(Icons.check_circle, color: AppColors.success)
                              : null,
                  errorText: _productCodeError,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(
                      color: _productCodeError != null ? AppColors.error : AppColors.gray300,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(
                      color: _productCodeError != null ? AppColors.error : AppColors.gray300,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(
                      color: _productCodeError != null ? AppColors.error : AppColors.primary,
                    ),
                  ),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى إدخال كود المنتج';
                  }
                  if (_productCodeError != null) {
                    return _productCodeError;
                  }
                  return null;
                },
              ),

              SizedBox(height: 20.h),

              // Description
              _buildSectionTitle('الوصف *'),
              SizedBox(height: 8.h),
              TextFormField(
                controller: _descriptionController,
                maxLines: 4,
                decoration: InputDecoration(
                  hintText: 'أدخل وصف المنتج',
                  prefixIcon: const Icon(Icons.description),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى إدخال وصف المنتج';
                  }
                  return null;
                },
              ),

              SizedBox(height: 20.h),



              // Original Price and Selling Price Row
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSectionTitle('السعر الأصلي'),
                        SizedBox(height: 8.h),
                        TextFormField(
                          controller: _originalPriceController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            hintText: '0.00',
                            prefixIcon: const Icon(Icons.price_change),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              if (double.tryParse(value) == null) {
                                return 'يرجى إدخال رقم صحيح';
                              }
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSectionTitle('سعر البيع (قبل الخصم) *'),
                        SizedBox(height: 8.h),
                        TextFormField(
                          controller: _sellingPriceController,
                          keyboardType: TextInputType.number,
                          onChanged: (value) => setState(() {}), // Refresh to recalculate discount
                          decoration: InputDecoration(
                            hintText: '0.00',
                            prefixIcon: const Icon(Icons.sell),
                            helperText: 'السعر قبل تطبيق الخصم',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return 'يرجى إدخال سعر البيع';
                            }
                            if (double.tryParse(value!) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              SizedBox(height: 20.h),

              // Discount and Stock Row
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSectionTitle('نسبة الخصم %'),
                        SizedBox(height: 8.h),
                        TextFormField(
                          controller: _discountController,
                          keyboardType: TextInputType.number,
                          onChanged: (value) => setState(() {}), // Refresh to recalculate discount
                          decoration: InputDecoration(
                            hintText: '0',
                            prefixIcon: const Icon(Icons.percent),
                            suffixText: '%',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                          validator: (value) {
                            if (value?.isNotEmpty ?? false) {
                              final discount = double.tryParse(value!);
                              if (discount == null) {
                                return 'رقم غير صحيح';
                              }
                              if (discount < 0 || discount > 100) {
                                return 'يجب أن تكون بين 0-100';
                              }
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSectionTitle('الكمية *'),
                        SizedBox(height: 8.h),
                        TextFormField(
                          controller: _stockController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            hintText: '0',
                            prefixIcon: const Icon(Icons.inventory_2),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return 'يرجى إدخال الكمية';
                            }
                            if (int.tryParse(value!) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Display calculated price after discount
              if (_sellingPriceController.text.isNotEmpty && _discountController.text.isNotEmpty)
                _buildDiscountedPriceDisplay(),

              SizedBox(height: 20.h),

              // Category Selection
              _buildSectionTitle('الفئة *'),
              SizedBox(height: 8.h),
              BlocBuilder<CategoriesBloc, CategoriesState>(
                builder: (context, state) {
                  if (state is CategoriesLoaded) {
                    return DropdownButtonFormField<String>(
                      value: _selectedCategoryId,
                      decoration: InputDecoration(
                        hintText: 'اختر الفئة',
                        prefixIcon: const Icon(Icons.category),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      items: state.categories.map((category) {
                        return DropdownMenuItem<String>(
                          value: category.id,
                          child: Text(category.name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCategoryId = value;
                        });
                      },
                      validator: (value) {
                        if (value?.isEmpty ?? true) {
                          return 'يرجى اختيار الفئة';
                        }
                        return null;
                      },
                    );
                  }
                  return const CircularProgressIndicator();
                },
              ),

              SizedBox(height: 20.h),

              // Images Section
              _buildSectionTitle('الصور * (صورة واحدة على الأقل)'),
              SizedBox(height: 8.h),
              _buildImagesSection(),

              SizedBox(height: 20.h),

              // Active Status
              Row(
                children: [
                  Switch(
                    value: _isActive,
                    onChanged: (value) {
                      setState(() {
                        _isActive = value;
                      });
                    },
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    'منتج نشط',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 40.h),
            ],
          ),
        ),
      ),
    ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: title.contains('*') ? title.replaceAll('*', '') : title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          if (title.contains('*'))
            TextSpan(
              text: ' *',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.error,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDiscountedPriceDisplay() {
    final price = double.tryParse(_sellingPriceController.text) ?? 0.0;
    final discount = double.tryParse(_discountController.text) ?? 0.0;

    if (price > 0 && discount > 0) {
      final discountedPrice = price - (price * discount / 100);

      return Container(
        margin: EdgeInsets.only(top: 12.h),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'السعر النهائي (بعد الخصم):',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            Text(
              '${discountedPrice.toStringAsFixed(2)} دينار',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.primary,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              'هذا هو السعر الذي سيُحفظ ويُستخدم في الفواتير',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildImagesSection() {
    // حساب العدد الإجمالي للصور (الموجودة + الجديدة)
    final existingImagesCount = widget.isEditing && widget.product != null
        ? widget.product!.images.length
        : 0;
    final totalImagesCount = existingImagesCount + _selectedImages.length;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray300),
      ),
      child: Column(
        children: [
          // Existing Images (في حالة التعديل)
          if (widget.isEditing && widget.product != null && widget.product!.images.isNotEmpty) ...[
            Text(
              'الصور الحالية',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 8.h),
            SizedBox(
              height: 100.h,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: widget.product!.images.length,
                itemBuilder: (context, index) {
                  final image = widget.product!.images[index];
                  return Container(
                    width: 100.w,
                    height: 100.h,
                    margin: EdgeInsets.only(right: 8.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: AppColors.gray300),
                    ),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8.r),
                          child: Image.network(
                            image.imageUrl,
                            width: 100.w,
                            height: 100.h,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: AppColors.gray100,
                                child: Icon(
                                  Icons.image_not_supported,
                                  size: 32.w,
                                  color: AppColors.gray400,
                                ),
                              );
                            },
                          ),
                        ),
                        // Primary badge
                        if (image.isPrimary)
                          Positioned(
                            top: 4.w,
                            left: 4.w,
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Text(
                                'رئيسية',
                                style: TextStyle(
                                  fontSize: 8.sp,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.white,
                                ),
                              ),
                            ),
                          ),
                        // Delete button
                        Positioned(
                          top: 4.w,
                          right: 4.w,
                          child: GestureDetector(
                            onTap: () => _deleteExistingImage(image),
                            child: Container(
                              padding: EdgeInsets.all(4.w),
                              decoration: const BoxDecoration(
                                color: AppColors.error,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.close,
                                color: AppColors.white,
                                size: 16.w,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: 16.h),
          ],

          // New Selected Images
          if (_selectedImages.isNotEmpty) ...[
            if (widget.isEditing && widget.product != null && widget.product!.images.isNotEmpty)
              Text(
                'الصور الجديدة',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            SizedBox(height: 8.h),
            SizedBox(
              height: 100.h,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    width: 100.w,
                    height: 100.h,
                    margin: EdgeInsets.only(right: 8.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: AppColors.gray300),
                    ),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8.r),
                          child: Image.file(
                            File(_selectedImages[index].path),
                            width: 100.w,
                            height: 100.h,
                            fit: BoxFit.cover,
                          ),
                        ),
                        Positioned(
                          top: 4.w,
                          right: 4.w,
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedImages.removeAt(index);
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.all(4.w),
                              decoration: const BoxDecoration(
                                color: AppColors.error,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.close,
                                color: AppColors.white,
                                size: 16.w,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: 16.h),
          ],

          // Add Images Button
          if (totalImagesCount < 5)
            ElevatedButton.icon(
              onPressed: _pickImages,
              icon: const Icon(Icons.add_photo_alternate),
              label: Text(totalImagesCount == 0 ? 'إضافة صور (مطلوبة)' : 'إضافة المزيد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: totalImagesCount == 0
                    ? AppColors.error.withValues(alpha: 0.1)
                    : AppColors.primary.withValues(alpha: 0.1),
                foregroundColor: totalImagesCount == 0 ? AppColors.error : AppColors.primary,
                elevation: 0,
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
              ),
            ),

          // رسالة تحذيرية عند عدم وجود صور
          if (totalImagesCount == 0) ...[
            SizedBox(height: 8.h),
            Text(
              'يجب إضافة صورة واحدة على الأقل للمنتج',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.error,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _pickImages() async {
    final remainingSlots = 5 - _selectedImages.length;
    final List<XFile> images = await _imagePicker.pickMultiImage(
      limit: remainingSlots,
    );

    if (images.isNotEmpty) {
      setState(() {
        _selectedImages.addAll(images);
      });
    }
  }

  void _saveProduct() async {
    if (_formKey.currentState!.validate() && _validateImages()) {
      // Show loading indicator
      LoadingDialog.show(
        context,
        _selectedImages.isNotEmpty
            ? (widget.isEditing ? 'جاري تحديث المنتج ورفع الصور...' : 'جاري حفظ المنتج ورفع الصور...')
            : (widget.isEditing ? 'جاري تحديث المنتج...' : 'جاري حفظ المنتج...'),
      );

      try {
        // حساب السعر النهائي بعد الخصم
        final originalSellingPrice = _sellingPriceController.text.isEmpty
            ? 0.0
            : double.parse(_sellingPriceController.text);
        final discountPercentage = _discountController.text.isEmpty
            ? 0.0
            : double.parse(_discountController.text);

        // السعر النهائي = السعر الأصلي - (السعر الأصلي × نسبة الخصم / 100)
        final finalSellingPrice = discountPercentage > 0
            ? originalSellingPrice - (originalSellingPrice * discountPercentage / 100)
            : originalSellingPrice;

        final product = ProductModel(
          id: widget.product?.id ?? '',
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          productCode: _productCodeController.text.trim(),
          originalPrice: _originalPriceController.text.isEmpty
              ? null
              : double.parse(_originalPriceController.text),
          sellingPrice: finalSellingPrice, // السعر بعد الخصم
          discountPercentage: discountPercentage,
          stock: int.parse(_stockController.text),
          categoryId: _selectedCategoryId!,
          isActive: _isActive,
          createdAt: widget.product?.createdAt ?? DateTime.now(),
          updatedAt: DateTime.now(),
          images: [], // Will be handled separately
          categoryModel: null, // Will be populated by repository
        );

        if (widget.isEditing) {
          context.read<ProductsBloc>().add(UpdateProduct(product: product));

          // Upload new images if any
          if (_selectedImages.isNotEmpty) {
            context.read<ProductsBloc>().add(UploadProductImages(
              productId: product.id,
              images: _selectedImages,
            ));
          }
        } else {
          context.read<ProductsBloc>().add(CreateProductWithImages(
            product: product,
            images: _selectedImages,
          ));
        }

        // Close loading dialog
        LoadingDialog.hide(context);

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.isEditing ? 'تم تحديث المنتج بنجاح' : 'تم حفظ المنتج بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );

        // Close form page
        Navigator.of(context).pop();
      } catch (e) {
        // Close loading dialog
        LoadingDialog.hide(context);

        // Show detailed error message
        String errorMessage = 'خطأ في حفظ المنتج';
        if (e.toString().contains('duplicate key')) {
          errorMessage = 'اسم المنتج موجود بالفعل';
        } else if (e.toString().contains('network')) {
          errorMessage = 'خطأ في الاتصال بالإنترنت';
        } else {
          errorMessage = 'خطأ في حفظ المنتج: ${e.toString()}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 5),
          ),
        );

        // Print error for debugging
        debugPrint('Product save error: $e');
      }
    }
  }

  bool _validateImages() {
    // حساب العدد الإجمالي للصور
    final existingImagesCount = widget.isEditing && widget.product != null
        ? widget.product!.images.length
        : 0;
    final totalImagesCount = existingImagesCount + _selectedImages.length;

    if (totalImagesCount == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة صورة واحدة على الأقل للمنتج'),
          backgroundColor: AppColors.error,
        ),
      );
      return false;
    }

    return true;
  }

  Future<void> _deleteExistingImage(ProductImageModel image) async {
    if (!mounted) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الصورة'),
        content: const Text('هل أنت متأكد من حذف هذه الصورة؟ سيتم حذفها نهائياً من التخزين.\n\nتذكر: يجب أن يحتوي المنتج على صورة واحدة على الأقل.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        // إظهار loading
        LoadingDialog.show(context, 'جاري حذف الصورة...');

        // حذف الصورة من المنتج
        if (mounted) {
          context.read<ProductsBloc>().add(DeleteProductImage(
            productId: widget.product!.id,
            imageId: image.id,
          ));
        }

        // إخفاء loading
        if (mounted) {
          LoadingDialog.hide(context);
        }

        // إعادة تحميل بيانات المنتج لتحديث UI
        if (mounted) {
          setState(() {
            // سيتم تحديث widget.product من خلال الـ Bloc
          });
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الصورة بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          LoadingDialog.hide(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في حذف الصورة: ${e.toString()}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }
}
