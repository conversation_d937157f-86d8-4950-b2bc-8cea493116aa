import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/models/holiday_model.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/utils/time_utils.dart';
import 'time_slot_selection_dialog.dart';
import '../../../appointments/data/repositories/appointments_repository.dart';
import '../../../appointments/data/repositories/time_slots_repository.dart';
import '../../../appointments/data/repositories/holidays_repository.dart';
import '../pages/print_appointment_invoice_page.dart';

class SingleAppointmentWidget extends StatefulWidget {
  final PatientModel patient;
  final VoidCallback? onPatientReset;

  const SingleAppointmentWidget({
    super.key,
    required this.patient,
    this.onPatientReset,
  });

  @override
  State<SingleAppointmentWidget> createState() => _SingleAppointmentWidgetState();
}

class _SingleAppointmentWidgetState extends State<SingleAppointmentWidget> {
  final AppointmentsRepository _appointmentsRepository = AppointmentsRepository();
  final TimeSlotsRepository _timeSlotsRepository = TimeSlotsRepository();
  final HolidaysRepository _holidaysRepository = HolidaysRepository();

  DateTime? _selectedDate;
  TimeSlotModel? _selectedTimeSlot;
  List<TimeSlotModel> _availableTimeSlots = [];

  bool _isLoadingTimeSlots = false;

  final TextEditingController _consultationFeeController = TextEditingController();
  final TextEditingController _paidAmountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void dispose() {
    _consultationFeeController.dispose();
    _paidAmountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // Check if the selected date is a holiday
      final holidays = await _checkHolidaysForDate(picked);
      if (holidays.isNotEmpty) {
        if (mounted) {
          _showHolidayDialog(picked, holidays);
        }
        return;
      }

      setState(() {
        _selectedDate = picked;
        _selectedTimeSlot = null;
        _availableTimeSlots = [];
      });

      await _loadAvailableTimeSlots();
    }
  }

  Future<List<HolidayModel>> _checkHolidaysForDate(DateTime date) async {
    try {
      final holidays = await _holidaysRepository.getHolidaysByDate(date);
      return holidays;
    } catch (e) {
      debugPrint('Error checking holidays: $e');
      return [];
    }
  }

  void _resetFormWithoutNavigation() {
    setState(() {
      _selectedDate = null;
      _selectedTimeSlot = null;
      _availableTimeSlots = [];
      _consultationFeeController.clear();
      _paidAmountController.clear();
      _notesController.clear();
    });
  }



  Future<void> _showTimeSlotSelectionDialog() async {
    if (_selectedDate == null || _availableTimeSlots.isEmpty) return;

    final selectedTimeSlot = await showDialog<TimeSlotModel>(
      context: context,
      builder: (context) => TimeSlotSelectionDialog(
        availableTimeSlots: _availableTimeSlots,
        selectedTimeSlot: _selectedTimeSlot,
        selectedDate: _selectedDate!,
      ),
    );

    if (selectedTimeSlot != null) {
      setState(() {
        _selectedTimeSlot = selectedTimeSlot;
      });
    }
  }

  void _showHolidayDialog(DateTime date, List<HolidayModel> holidays) {
    final dayName = TimeUtils.getArabicDayName(date.weekday);
    final dateStr = '${date.day}/${date.month}/${date.year}';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.event_busy, color: AppColors.error, size: 24.sp),
            SizedBox(width: 8.w),
            Text(
              'يوم إجازة',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.error,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$dayName - $dateStr',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              'هذا التاريخ يوافق إجازة رسمية:',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 8.h),
            ...holidays.map((holiday) => Container(
              margin: EdgeInsets.only(bottom: 8.h),
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    holiday.occasionName,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.error,
                    ),
                  ),
                  if (holiday.notes != null && holiday.notes!.isNotEmpty)
                    Text(
                      holiday.notes!,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                ],
              ),
            )),
            SizedBox(height: 8.h),
            Text(
              'يرجى اختيار تاريخ آخر للحجز.',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'موافق',
              style: TextStyle(color: AppColors.primary),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _loadAvailableTimeSlots() async {
    if (_selectedDate == null) return;

    setState(() {
      _isLoadingTimeSlots = true;
    });

    try {
      final timeSlots = await _timeSlotsRepository.getAvailableTimeSlots(_selectedDate!);
      setState(() {
        _availableTimeSlots = timeSlots;
        _isLoadingTimeSlots = false;
      });
    } catch (e) {
      setState(() {
        _availableTimeSlots = [];
        _isLoadingTimeSlots = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الأوقات المتاحة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _bookAppointment() async {
    if (_selectedDate == null || _selectedTimeSlot == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى اختيار التاريخ والوقت'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // Validate payment fields
    if (_consultationFeeController.text.isEmpty || _paidAmountController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى إدخال سعر الكشف والمبلغ المدفوع'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final consultationFee = double.tryParse(_consultationFeeController.text);
    final paidAmount = double.tryParse(_paidAmountController.text);

    if (consultationFee == null || paidAmount == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى إدخال أرقام صحيحة للمبالغ'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    try {
      final createdAppointment = await _appointmentsRepository.createSingleAppointment(
        patientId: widget.patient.id,
        timeSlotId: _selectedTimeSlot!.id,
        appointmentDate: _selectedDate!,
        consultationFee: consultationFee,
        paidAmount: paidAmount,
        notes: _notesController.text.trim(),
      );

      if (mounted) {
        _showSuccessDialog(createdAppointment);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حجز الموعد: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showSuccessDialog(AppointmentModel appointment) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.success, size: 24.sp),
            SizedBox(width: 8.w),
            Text(
              'تم الحجز بنجاح',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
          ],
        ),
        content: Text(
          'تم حجز الموعد بنجاح. هل تريد طباعة فاتورة الحجز؟',
          style: TextStyle(fontSize: 14.sp),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              _resetFormWithoutNavigation(); // Reset form only
            },
            child: Text(
              'لا، شكراً',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => PrintAppointmentInvoicePage(
                    appointment: appointment,
                    patient: widget.patient,
                  ),
                ),
              ).then((_) {
                // Close dialog first, then reset patient
                if (mounted) {
                  final navigator = Navigator.of(context);
                  navigator.pop(); // Close dialog
                  // Reset patient immediately after closing dialog
                  Future.microtask(() {
                    widget.onPatientReset?.call();
                  });
                }
              });
            },
            icon: Icon(Icons.print, size: 16.sp, color: AppColors.white),
            label: Text(
              'طباعة الفاتورة',
              style: TextStyle(color: AppColors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date Selection
          _buildSectionTitle('اختيار التاريخ'),
          SizedBox(height: 8.h),
          InkWell(
            onTap: _selectDate,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.primary),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Row(
                children: [
                  Icon(Icons.calendar_today, color: AppColors.primary),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      _selectedDate == null
                          ? 'اختر التاريخ'
                          : '${TimeUtils.getArabicDayName(_selectedDate!.weekday)} - ${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: _selectedDate == null
                            ? AppColors.textSecondary
                            : AppColors.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 20.h),

          // Time Slots Selection
          if (_selectedDate != null) ...[
            _buildSectionTitle('اختيار الوقت'),
            SizedBox(height: 8.h),
            if (_isLoadingTimeSlots)
              Center(
                child: CircularProgressIndicator(color: AppColors.primary),
              )
            else if (_availableTimeSlots.isEmpty)
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  color: AppColors.error.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  'لا توجد أوقات متاحة في هذا التاريخ',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.error,
                  ),
                  textAlign: TextAlign.center,
                ),
              )
            else
              InkWell(
                onTap: () => _showTimeSlotSelectionDialog(),
                borderRadius: BorderRadius.circular(12.r),
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: _selectedTimeSlot != null
                          ? AppColors.primary
                          : AppColors.primary.withValues(alpha: 0.3),
                      width: _selectedTimeSlot != null ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(12.r),
                    color: _selectedTimeSlot != null
                        ? AppColors.primary.withValues(alpha: 0.05)
                        : AppColors.white,
                  ),
                  child: _selectedTimeSlot == null
                      ? Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(8.w),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Icon(
                                Icons.access_time,
                                color: AppColors.primary,
                                size: 20.sp,
                              ),
                            ),
                            SizedBox(width: 12.w),
                            Text(
                              'اختر الوقت المناسب',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: AppColors.textSecondary,
                              ),
                            ),
                            const Spacer(),
                            Icon(
                              Icons.keyboard_arrow_down,
                              color: AppColors.primary,
                              size: 24.sp,
                            ),
                          ],
                        )
                      : Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(8.w),
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Icon(
                                Icons.schedule,
                                color: AppColors.white,
                                size: 20.sp,
                              ),
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    TimeUtils.formatTimeRange(_selectedTimeSlot!.startTime, _selectedTimeSlot!.endTime),
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                  if (_selectedTimeSlot!.employeeName != null)
                                    Text(
                                      'د. ${_selectedTimeSlot!.employeeName}${_selectedTimeSlot!.specialization != null ? ' - ${_selectedTimeSlot!.specialization}' : ''}',
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            Icon(
                              Icons.edit,
                              color: AppColors.primary,
                              size: 20.sp,
                            ),
                          ],
                        ),
                ),
              ),
          ],

          SizedBox(height: 20.h),

          // Payment Information
          if (_selectedTimeSlot != null) ...[
            _buildSectionTitle('معلومات الدفع'),
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _consultationFeeController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'سعر الكشف *',
                      hintText: '0.00',
                      suffixText: 'د.ا',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      prefixIcon: Icon(Icons.attach_money, color: AppColors.primary),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال سعر الكشف';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: TextFormField(
                    controller: _paidAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'المبلغ المدفوع *',
                      hintText: '0.00',
                      suffixText: 'د.ا',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      prefixIcon: Icon(Icons.payment, color: AppColors.success),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال المبلغ المدفوع';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),

            SizedBox(height: 12.h),

            // Notes
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),

            SizedBox(height: 20.h),

            // Book Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _bookAppointment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: Text(
                  'حجز الموعد',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }
}
