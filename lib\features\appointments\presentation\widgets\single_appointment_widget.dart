import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/models/holiday_model.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../appointments/data/repositories/appointments_repository.dart';
import '../../../appointments/data/repositories/time_slots_repository.dart';
import '../../../appointments/data/repositories/holidays_repository.dart';
import '../pages/print_appointment_invoice_page.dart';

class SingleAppointmentWidget extends StatefulWidget {
  final PatientModel patient;

  const SingleAppointmentWidget({
    super.key,
    required this.patient,
  });

  @override
  State<SingleAppointmentWidget> createState() => _SingleAppointmentWidgetState();
}

class _SingleAppointmentWidgetState extends State<SingleAppointmentWidget> {
  final AppointmentsRepository _appointmentsRepository = AppointmentsRepository();
  final TimeSlotsRepository _timeSlotsRepository = TimeSlotsRepository();
  final HolidaysRepository _holidaysRepository = HolidaysRepository();

  DateTime? _selectedDate;
  TimeSlotModel? _selectedTimeSlot;
  List<TimeSlotModel> _availableTimeSlots = [];
  bool _isLoadingTimeSlots = false;
  
  final TextEditingController _consultationFeeController = TextEditingController();
  final TextEditingController _paidAmountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void dispose() {
    _consultationFeeController.dispose();
    _paidAmountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // Check if the selected date is a holiday
      final isHoliday = await _checkIfHoliday(picked);
      if (isHoliday) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('التاريخ المحدد يوافق إجازة، يرجى اختيار تاريخ آخر'),
              backgroundColor: AppColors.error,
            ),
          );
        }
        return;
      }

      setState(() {
        _selectedDate = picked;
        _selectedTimeSlot = null;
        _availableTimeSlots = [];
      });
      
      await _loadAvailableTimeSlots();
    }
  }

  Future<bool> _checkIfHoliday(DateTime date) async {
    try {
      final holidays = await _holidaysRepository.getHolidaysByDate(date);
      return holidays.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking holidays: $e');
      return false;
    }
  }

  Future<void> _loadAvailableTimeSlots() async {
    if (_selectedDate == null) return;

    setState(() {
      _isLoadingTimeSlots = true;
    });

    try {
      final timeSlots = await _timeSlotsRepository.getAvailableTimeSlots(_selectedDate!);
      setState(() {
        _availableTimeSlots = timeSlots;
        _isLoadingTimeSlots = false;
      });
    } catch (e) {
      setState(() {
        _availableTimeSlots = [];
        _isLoadingTimeSlots = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الأوقات المتاحة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _bookAppointment() async {
    if (_selectedDate == null || _selectedTimeSlot == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى اختيار التاريخ والوقت'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final consultationFee = double.tryParse(_consultationFeeController.text) ?? 0.0;
    final paidAmount = double.tryParse(_paidAmountController.text) ?? 0.0;

    try {
      final createdAppointment = await _appointmentsRepository.createSingleAppointment(
        patientId: widget.patient.id,
        timeSlotId: _selectedTimeSlot!.id,
        appointmentDate: _selectedDate!,
        consultationFee: consultationFee,
        paidAmount: paidAmount,
        notes: _notesController.text.trim(),
      );

      if (mounted) {
        _showSuccessDialog(createdAppointment);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حجز الموعد: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showSuccessDialog(AppointmentModel appointment) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.success, size: 24.sp),
            SizedBox(width: 8.w),
            Text(
              'تم الحجز بنجاح',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
          ],
        ),
        content: Text(
          'تم حجز الموعد بنجاح. هل تريد طباعة فاتورة الحجز؟',
          style: TextStyle(fontSize: 14.sp),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to appointments
            },
            child: Text(
              'لا، شكراً',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => PrintAppointmentInvoicePage(
                    appointment: appointment,
                    patient: widget.patient,
                  ),
                ),
              ).then((_) {
                if (mounted) {
                  Navigator.of(context).pop(); // Go back to appointments after printing
                }
              });
            },
            icon: Icon(Icons.print, size: 16.sp, color: AppColors.white),
            label: Text(
              'طباعة الفاتورة',
              style: TextStyle(color: AppColors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date Selection
          _buildSectionTitle('اختيار التاريخ'),
          SizedBox(height: 8.h),
          InkWell(
            onTap: _selectDate,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.primary),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Row(
                children: [
                  Icon(Icons.calendar_today, color: AppColors.primary),
                  SizedBox(width: 12.w),
                  Text(
                    _selectedDate == null
                        ? 'اختر التاريخ'
                        : '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: _selectedDate == null 
                          ? AppColors.textSecondary 
                          : AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 20.h),

          // Time Slots Selection
          if (_selectedDate != null) ...[
            _buildSectionTitle('اختيار الوقت'),
            SizedBox(height: 8.h),
            if (_isLoadingTimeSlots)
              Center(
                child: CircularProgressIndicator(color: AppColors.primary),
              )
            else if (_availableTimeSlots.isEmpty)
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  color: AppColors.error.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  'لا توجد أوقات متاحة في هذا التاريخ',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.error,
                  ),
                  textAlign: TextAlign.center,
                ),
              )
            else
              SizedBox(
                height: 200.h,
                child: ListView.builder(
                  itemCount: _availableTimeSlots.length,
                  itemBuilder: (context, index) {
                    final timeSlot = _availableTimeSlots[index];
                    final isSelected = _selectedTimeSlot?.id == timeSlot.id;
                    
                    return Container(
                      margin: EdgeInsets.only(bottom: 8.h),
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            _selectedTimeSlot = timeSlot;
                          });
                        },
                        child: Container(
                          padding: EdgeInsets.all(12.w),
                          decoration: BoxDecoration(
                            color: isSelected 
                                ? AppColors.primary.withValues(alpha: 0.1)
                                : AppColors.white,
                            border: Border.all(
                              color: isSelected 
                                  ? AppColors.primary 
                                  : AppColors.primary.withValues(alpha: 0.3),
                              width: isSelected ? 2 : 1,
                            ),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                color: isSelected 
                                    ? AppColors.primary 
                                    : AppColors.textSecondary,
                              ),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '${timeSlot.startTime} - ${timeSlot.endTime}',
                                      style: TextStyle(
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.bold,
                                        color: isSelected 
                                            ? AppColors.primary 
                                            : AppColors.textPrimary,
                                      ),
                                    ),
                                    if (timeSlot.employeeName != null)
                                      Text(
                                        'د. ${timeSlot.employeeName}',
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          color: AppColors.textSecondary,
                                        ),
                                      ),
                                    if (timeSlot.specialization != null)
                                      Text(
                                        timeSlot.specialization!,
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: AppColors.textSecondary,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              if (isSelected)
                                Icon(
                                  Icons.check_circle,
                                  color: AppColors.primary,
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],

          SizedBox(height: 20.h),

          // Payment Information
          if (_selectedTimeSlot != null) ...[
            _buildSectionTitle('معلومات الدفع'),
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _consultationFeeController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'سعر الكشف',
                      suffixText: 'د.ا',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: TextFormField(
                    controller: _paidAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'المبلغ المدفوع',
                      suffixText: 'د.ا',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: 12.h),

            // Notes
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),

            SizedBox(height: 20.h),

            // Book Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _bookAppointment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: Text(
                  'حجز الموعد',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }
}
