import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/product_model.dart';
import '../../../../core/models/sales_invoice_model.dart';
import '../../../../core/models/invoice_item_model.dart';

import '../bloc/sales_bloc.dart';
import '../bloc/sales_event.dart';
import '../bloc/sales_state.dart';
import '../widgets/patient_selector_widget.dart';
import '../widgets/product_selector_widget.dart';
import 'print_invoice_page.dart';
import '../widgets/invoice_items_widget.dart';
import '../widgets/invoice_summary_widget.dart';

class CreateInvoicePage extends StatefulWidget {
  final bool isVisible;
  final bool hasBeenVisited;

  const CreateInvoicePage({
    super.key,
    required this.isVisible,
    required this.hasBeenVisited,
  });

  @override
  State<CreateInvoicePage> createState() => _CreateInvoicePageState();
}

class _CreateInvoicePageState extends State<CreateInvoicePage> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final _discountController = TextEditingController();
  final _dueDateController = TextEditingController();
  final _paidAmountController = TextEditingController();
  final _remainingAmountController = TextEditingController();

  // Invoice data
  String? _invoiceNumber;
  PatientModel? _selectedPatient;
  PaymentType _paymentType = PaymentType.cash;
  final List<InvoiceItemModel> _invoiceItems = [];
  double _discountPercentage = 0.0;
  DateTime? _dueDate;
  bool _isCommissionSale = false;
  double _paidAmount = 0.0;
  double _remainingAmount = 0.0;

  @override
  void initState() {
    super.initState();
    _generateInvoiceNumber();
  }

  @override
  void dispose() {
    _notesController.dispose();
    _discountController.dispose();
    _dueDateController.dispose();
    _paidAmountController.dispose();
    _remainingAmountController.dispose();
    super.dispose();
  }

  void _generateInvoiceNumber() {
    context.read<SalesBloc>().add(GenerateInvoiceNumber());
  }

  double _calculateSubtotal() {
    return _invoiceItems.fold<double>(
      0.0,
      (sum, item) => sum + item.finalPrice,
    );
  }

  void _updateRemainingAmount() {
    final subtotal = _calculateSubtotal();
    final discountAmount = subtotal * (_discountPercentage / 100);
    final finalAmount = subtotal - discountAmount;

    // For installment: remaining = final amount (after discount) - paid amount
    // For cash: remaining = 0
    if (_paymentType == PaymentType.installment) {
      final remaining = finalAmount - _paidAmount;
      _remainingAmount = remaining > 0 ? remaining : 0.0;
      _remainingAmountController.text = _remainingAmount.toStringAsFixed(2);
    } else {
      _remainingAmount = 0.0;
      _remainingAmountController.text = '0.00';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible && !widget.hasBeenVisited) {
      return const SizedBox.shrink();
    }

    return Scaffold(
      body: BlocListener<SalesBloc, SalesState>(
        listener: (context, state) {
          if (state is NumberGenerated && state.type == 'invoice') {
            setState(() {
              _invoiceNumber = state.generatedNumber;
            });
          } else if (state is InvoiceCreated) {
            _showSuccessDialog(state.invoice);
          } else if (state is SalesError) {
            _showErrorDialog(state.message);
          }
        },
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Header
                _buildHeader(),

                // Content
                Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Invoice Number
                      _buildInvoiceNumberSection(),
                      
                      SizedBox(height: 20.h),
                      
                      // Patient Selection
                      _buildPatientSection(),

                      SizedBox(height: 20.h),

                      // Product Selection
                      _buildProductSection(),

                      SizedBox(height: 20.h),

                      // Invoice Items
                      _buildInvoiceItemsSection(),

                      SizedBox(height: 20.h),

                      // Discount
                      _buildDiscountSection(),

                      SizedBox(height: 20.h),

                      // Payment Type
                      _buildPaymentTypeSection(),

                      // Due Date (for installment) - Optional
                      if (_paymentType == PaymentType.installment) ...[
                        SizedBox(height: 16.h),
                        _buildDueDateSection(),
                      ],

                      // Payment amounts (for installment)
                      if (_paymentType == PaymentType.installment) ...[
                        SizedBox(height: 16.h),
                        _buildPaymentAmountsSection(),
                      ],
                      
                      SizedBox(height: 20.h),
                      
                      // Notes
                      _buildNotesSection(),
                      
                      SizedBox(height: 20.h),
                      
                      // Summary
                      _buildSummarySection(),
                      
                      SizedBox(height: 30.h),

                      // Create Button
                      _buildCreateButton(),

                      SizedBox(height: 20.h),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.add_shopping_cart,
            color: AppColors.primary,
            size: 24.sp,
          ),
          SizedBox(width: 12.w),
          Text(
            'إنشاء فاتورة جديدة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: _generateInvoiceNumber,
            icon: Icon(
              Icons.refresh,
              color: AppColors.primary,
              size: 20.sp,
            ),
            tooltip: 'توليد رقم جديد',
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceNumberSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            Icon(
              Icons.receipt_long,
              color: AppColors.primary,
              size: 20.sp,
            ),
            SizedBox(width: 12.w),
            Text(
              'رقم الفاتورة:',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(width: 8.w),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: 12.w,
                vertical: 6.h,
              ),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                _invoiceNumber ?? 'جاري التحميل...',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPatientSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'اختيار المريض *',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            PatientSelectorWidget(
              selectedPatient: _selectedPatient,
              onPatientSelected: (patient) {
                setState(() {
                  _selectedPatient = patient;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentTypeSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.payment,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'نوع الدفع *',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<PaymentType>(
                    title: Text(
                      PaymentType.cash.arabicName,
                      style: TextStyle(fontSize: 14.sp),
                    ),
                    value: PaymentType.cash,
                    groupValue: _paymentType,
                    onChanged: (value) {
                      setState(() {
                        _paymentType = value!;
                        if (value == PaymentType.cash) {
                          _dueDate = null;
                          _dueDateController.clear();
                          _paidAmount = 0.0;
                          _remainingAmount = 0.0;
                          _paidAmountController.clear();
                          _remainingAmountController.clear();
                        }
                        _updateRemainingAmount();
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<PaymentType>(
                    title: Text(
                      PaymentType.installment.arabicName,
                      style: TextStyle(fontSize: 14.sp),
                    ),
                    value: PaymentType.installment,
                    groupValue: _paymentType,
                    onChanged: (value) {
                      setState(() {
                        _paymentType = value!;
                        _updateRemainingAmount();
                      });
                    },
                  ),
                ),
              ],
            ),

            // Commission Sale Option
            SizedBox(height: 16.h),
            CheckboxListTile(
              title: Text(
                'بيع بعمولة (5%)',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              subtitle: Text(
                'سيتم حساب 5% عمولة من إجمالي الفاتورة (للمعلومات فقط)',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.textSecondary,
                ),
              ),
              value: _isCommissionSale,
              onChanged: (value) {
                setState(() {
                  _isCommissionSale = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDueDateSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'تاريخ الاستحقاق (اختياري)',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            TextFormField(
              controller: _dueDateController,
              readOnly: true,
              decoration: InputDecoration(
                hintText: 'اختر تاريخ الاستحقاق',
                suffixIcon: const Icon(Icons.calendar_today),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now().add(const Duration(days: 30)),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (date != null) {
                  setState(() {
                    _dueDate = date;
                    _dueDateController.text = '${date.day}/${date.month}/${date.year}';
                  });
                }
              },
              // No validation - due date is optional
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentAmountsSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.payments,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'تفاصيل الدفع',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _paidAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'المبلغ المدفوع',
                      hintText: '0.00',
                      prefixIcon: const Icon(Icons.attach_money),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    onChanged: (value) {
                      final paidAmount = double.tryParse(value) ?? 0.0;
                      setState(() {
                        _paidAmount = paidAmount;
                        _updateRemainingAmount();
                      });
                    },
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: TextFormField(
                    controller: _remainingAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'المبلغ المتبقي',
                      hintText: '0.00',
                      prefixIcon: const Icon(Icons.money_off),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    onChanged: (value) {
                      final remainingAmount = double.tryParse(value) ?? 0.0;
                      setState(() {
                        _remainingAmount = remainingAmount;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.shopping_cart,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'إضافة منتجات',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            ProductSelectorWidget(
              onProductSelected: _addProductToInvoice,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceItemsSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.list,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'عناصر الفاتورة',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                Text(
                  '(${_invoiceItems.length} عنصر)',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            InvoiceItemsWidget(
              items: _invoiceItems,
              onItemUpdated: _updateInvoiceItem,
              onItemRemoved: _removeInvoiceItem,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscountSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.percent,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'خصم إضافي',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            TextFormField(
              controller: _discountController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: '0',
                suffixText: '%',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _discountPercentage = double.tryParse(value) ?? 0.0;
                });
              },
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final discount = double.tryParse(value);
                  if (discount == null || discount < 0 || discount > 100) {
                    return 'يرجى إدخال نسبة خصم صحيحة (0-100)';
                  }
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'ملاحظات',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                hintText: 'أضف ملاحظات إضافية...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummarySection() {
    return InvoiceSummaryWidget(
      items: _invoiceItems,
      discountPercentage: _discountPercentage,
      isCommissionSale: _isCommissionSale,
      paymentType: _paymentType,
      paidAmount: _paidAmount,
      remainingAmount: _remainingAmount,
      invoiceStatus: InvoiceStatus.active, // New invoices are always active
      returnAmount: 0.0, // New invoices have no return amount
    );
  }

  Widget _buildCreateButton() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BlocBuilder<SalesBloc, SalesState>(
        builder: (context, state) {
          final isLoading = state is SalesLoading;

          return SizedBox(
            width: double.infinity,
            height: 50.h,
            child: ElevatedButton(
              onPressed: isLoading ? null : _createInvoice,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: isLoading
                  ? SizedBox(
                      width: 20.w,
                      height: 20.h,
                      child: const CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text(
                      'إنشاء الفاتورة',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
            ),
          );
        },
      ),
    );
  }

  // Helper methods
  void _addProductToInvoice(ProductModel product, int quantity) {
    final existingIndex = _invoiceItems.indexWhere(
      (item) => item.productId == product.id,
    );

    if (existingIndex != -1) {
      // Update existing item
      final existingItem = _invoiceItems[existingIndex];
      final newQuantity = existingItem.quantity + quantity;
      final newItem = existingItem.copyWith(
        quantity: newQuantity,
        totalPrice: newQuantity * existingItem.unitPrice,
        finalPrice: newQuantity * existingItem.unitPrice,
      );

      setState(() {
        _invoiceItems[existingIndex] = newItem;
      });
    } else {
      // Add new item
      final unitPrice = product.effectivePrice; // العميل يدفع السعر العادي
      final totalPrice = quantity * unitPrice;

      final newItem = InvoiceItemModel(
        id: const Uuid().v4(), // Generate proper UUID
        invoiceId: 'temp-${DateTime.now().millisecondsSinceEpoch}', // Temporary ID, will be updated when invoice is created
        productId: product.id,
        product: product,
        productName: product.name,
        productCode: product.productCode,
        quantity: quantity,
        unitPrice: unitPrice,
        totalPrice: totalPrice,
        finalPrice: totalPrice,
        createdAt: DateTime.now(),
      );

      setState(() {
        _invoiceItems.add(newItem);
      });
    }
  }

  void _updateInvoiceItem(InvoiceItemModel updatedItem) {
    final index = _invoiceItems.indexWhere(
      (item) => item.id == updatedItem.id,
    );

    if (index != -1) {
      setState(() {
        _invoiceItems[index] = updatedItem;
      });
    }
  }

  void _removeInvoiceItem(String itemId) {
    setState(() {
      _invoiceItems.removeWhere((item) => item.id == itemId);
    });
  }

  void _createInvoice() {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedPatient == null) {
      _showErrorDialog('يرجى اختيار المريض');
      return;
    }

    if (_invoiceItems.isEmpty) {
      _showErrorDialog('يرجى إضافة منتجات للفاتورة');
      return;
    }

    if (_invoiceNumber == null) {
      _showErrorDialog('رقم الفاتورة غير متوفر');
      return;
    }

    // Calculate totals
    final subtotal = _invoiceItems.fold<double>(
      0.0,
      (sum, item) => sum + item.finalPrice,
    );

    final discountAmount = subtotal * (_discountPercentage / 100);
    final finalAmount = subtotal - discountAmount;

    // Calculate commission
    final commissionAmount = _isCommissionSale ? finalAmount * 0.05 : 0.0;

    // Calculate payment amounts
    final paidAmount = _paymentType == PaymentType.installment ? _paidAmount : finalAmount;
    final remainingAmount = _paymentType == PaymentType.installment ? _remainingAmount : 0.0;

    // Create invoice
    final invoice = SalesInvoiceModel(
      id: const Uuid().v4(), // Generate proper UUID
      invoiceNumber: _invoiceNumber!,
      patientId: _selectedPatient!.id,
      patient: _selectedPatient,
      totalAmount: subtotal,
      discountPercentage: _discountPercentage,
      discountAmount: discountAmount,
      finalAmount: finalAmount,
      isCommissionSale: _isCommissionSale,
      commissionPercentage: _isCommissionSale ? 5.0 : 0.0,
      commissionAmount: commissionAmount,
      paymentType: _paymentType,
      paymentStatus: PaymentStatusExtension.calculateStatus(
        paymentType: _paymentType,
        invoiceStatus: InvoiceStatus.active,
        remainingAmount: remainingAmount,
      ),
      paidAmount: paidAmount,
      remainingAmount: remainingAmount,
      dueDate: _dueDate,
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      status: InvoiceStatus.active,
      createdBy: const Uuid().v4(), // TODO: Get from current user - temporary UUID
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    context.read<SalesBloc>().add(CreateInvoice(
      invoice: invoice,
      items: _invoiceItems,
    ));
  }

  void _showSuccessDialog(SalesInvoiceModel invoice) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 24.sp),
            SizedBox(width: 8.w),
            Text('تم إنشاء الفاتورة بنجاح'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('رقم الفاتورة: ${invoice.invoiceNumber}'),
            SizedBox(height: 8.h),
            Text('المجموع: ${invoice.totalAmount.toStringAsFixed(2)} د.ا'),
            SizedBox(height: 8.h),
            Text('المريض: ${invoice.patient?.name ?? 'غير محدد'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetForm();
            },
            child: const Text('إنشاء فاتورة جديدة'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => PrintInvoicePage(invoice: invoice),
                ),
              );
            },
            icon: Icon(Icons.print, size: 16.sp),
            label: const Text('عرض وطباعة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _resetForm() {
    setState(() {
      _selectedPatient = null;
      _paymentType = PaymentType.cash;
      _invoiceItems.clear();
      _discountPercentage = 0.0;
      _dueDate = null;
      _isCommissionSale = false;
      _paidAmount = 0.0;
      _remainingAmount = 0.0;
    });

    _notesController.clear();
    _discountController.clear();
    _dueDateController.clear();
    _paidAmountController.clear();
    _remainingAmountController.clear();

    _generateInvoiceNumber();
  }
}
