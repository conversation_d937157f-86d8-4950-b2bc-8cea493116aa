import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/appointment_model.dart';
import 'time_slots_repository.dart';

class AppointmentsRepository {
  Future<List<AppointmentModel>> getAppointmentsByDate(DateTime date) async {
    try {
      final dateString = date.toIso8601String().split('T')[0];
      debugPrint('🔍 AppointmentsRepository: Loading appointments for date: $dateString');

      debugPrint('📡 AppointmentsRepository: Executing query...');
      final response = await SupabaseConfig.appointments
          .select()
          .eq('appointment_date', dateString)
          .order('created_at', ascending: true);

      debugPrint('📊 AppointmentsRepository: Raw response: $response');
      debugPrint('📊 AppointmentsRepository: Response length: ${response.length}');

      if (response.isEmpty) {
        debugPrint('⚠️ AppointmentsRepository: No appointments found for date $dateString');
        return [];
      }

      debugPrint('🔄 AppointmentsRepository: Parsing ${response.length} appointments...');
      final appointments = <AppointmentModel>[];

      for (int i = 0; i < response.length; i++) {
        try {
          final json = response[i];
          debugPrint('🔄 AppointmentsRepository: Parsing appointment $i: $json');
          final appointment = AppointmentModel.fromJson(json);
          appointments.add(appointment);
          debugPrint('✅ AppointmentsRepository: Successfully parsed appointment $i');
        } catch (e) {
          debugPrint('❌ AppointmentsRepository: Error parsing appointment $i: $e');
          // Continue with other appointments
        }
      }

      debugPrint('✅ AppointmentsRepository: Successfully parsed ${appointments.length} appointments');
      return appointments;
    } catch (e, stackTrace) {
      debugPrint('❌ AppointmentsRepository: Error loading appointments: $e');
      debugPrint('📍 AppointmentsRepository: Stack trace: $stackTrace');
      throw Exception('فشل في جلب المواعيد: ${e.toString()}');
    }
  }

  Future<List<AppointmentModel>> getPatientAppointments(String patientId) async {
    try {
      final response = await SupabaseConfig.appointments
          .select()
          .eq('patient_id', patientId)
          .order('appointment_date', ascending: false);

      return response.map<AppointmentModel>((json) => AppointmentModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب مواعيد المريض: ${e.toString()}');
    }
  }

  Future<List<AppointmentModel>> getTodayAppointments() async {
    try {
      final today = DateTime.now();
      return await getAppointmentsByDate(today);
    } catch (e) {
      throw Exception('فشل في جلب مواعيد اليوم: ${e.toString()}');
    }
  }

  Future<AppointmentModel> createAppointment(AppointmentModel appointment) async {
    try {
      debugPrint('🔄 AppointmentsRepository: Creating appointment...');
      debugPrint('👤 AppointmentsRepository: Patient ID: ${appointment.patientId}');
      debugPrint('⏰ AppointmentsRepository: Time Slot ID: ${appointment.timeSlotId}');
      debugPrint('📅 AppointmentsRepository: Date: ${appointment.appointmentDate}');
      debugPrint('📊 AppointmentsRepository: Status: ${appointment.status}');

      final jsonData = appointment.toJson();
      debugPrint('📤 AppointmentsRepository: Sending JSON: $jsonData');

      final response = await SupabaseConfig.appointments
          .insert(jsonData)
          .select()
          .single();

      debugPrint('📥 AppointmentsRepository: Response: $response');

      final createdAppointment = AppointmentModel.fromJson(response);
      debugPrint('✅ AppointmentsRepository: Successfully created appointment: ${createdAppointment.id}');

      return createdAppointment;
    } catch (e, stackTrace) {
      debugPrint('❌ AppointmentsRepository: Error creating appointment: $e');
      debugPrint('📍 AppointmentsRepository: Stack trace: $stackTrace');
      throw Exception('فشل في إنشاء الموعد: ${e.toString()}');
    }
  }

  Future<AppointmentModel> updateAppointment(AppointmentModel appointment) async {
    try {
      final response = await SupabaseConfig.appointments
          .update(appointment.toJson())
          .eq('id', appointment.id)
          .select()
          .single();

      return AppointmentModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تحديث الموعد: ${e.toString()}');
    }
  }

  Future<void> deleteAppointment(String id) async {
    try {
      await SupabaseConfig.appointments
          .delete()
          .eq('id', id);
    } catch (e) {
      throw Exception('فشل في حذف الموعد: ${e.toString()}');
    }
  }

  Future<AppointmentModel> updateAppointmentStatus(String id, String status) async {
    try {
      final response = await SupabaseConfig.appointments
          .update({'status': status})
          .eq('id', id)
          .select()
          .single();

      return AppointmentModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تحديث حالة الموعد: ${e.toString()}');
    }
  }

  Future<List<AppointmentModel>> getUpcomingAppointments() async {
    try {
      final today = DateTime.now().toIso8601String().split('T')[0];
      final response = await SupabaseConfig.appointments
          .select()
          .gte('appointment_date', today)
          .eq('status', 'scheduled')
          .order('appointment_date', ascending: true);

      return response.map<AppointmentModel>((json) => AppointmentModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب المواعيد القادمة: ${e.toString()}');
    }
  }

  Future<List<AppointmentModel>> getAppointmentsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final startDateString = startDate.toIso8601String().split('T')[0];
      final endDateString = endDate.toIso8601String().split('T')[0];

      final response = await SupabaseConfig.appointments
          .select()
          .gte('appointment_date', startDateString)
          .lte('appointment_date', endDateString)
          .order('appointment_date', ascending: true);

      return response.map<AppointmentModel>((json) => AppointmentModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب المواعيد: ${e.toString()}');
    }
  }

  // Create single appointment
  Future<AppointmentModel> createSingleAppointment({
    required String patientId,
    required String timeSlotId,
    required DateTime appointmentDate,
    required double consultationFee,
    required double paidAmount,
    String? notes,
  }) async {
    try {
      debugPrint('🔍 AppointmentsRepository: Creating single appointment...');

      // Get employee_id from time slot
      final timeSlotsRepo = TimeSlotsRepository();
      final timeSlot = await timeSlotsRepo.getTimeSlotById(timeSlotId);
      final employeeId = timeSlot?.employeeId;

      final remainingAmount = consultationFee - paidAmount;

      final appointmentData = {
        'patient_id': patientId,
        'time_slot_id': timeSlotId,
        'employee_id': employeeId,
        'appointment_date': appointmentDate.toIso8601String().split('T')[0],
        'status': AppointmentModel.statusConfirmed,
        'appointment_type': 'consultation',
        'duration_minutes': 30,
        'consultation_fee': consultationFee,
        'paid_amount': paidAmount,
        'remaining_amount': remainingAmount,
        'is_multiple_booking': false,
        'notes': notes,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await SupabaseConfig.appointments
          .insert(appointmentData)
          .select()
          .single();

      debugPrint('✅ AppointmentsRepository: Single appointment created successfully');

      return AppointmentModel.fromJson(response);
    } catch (e) {
      debugPrint('❌ AppointmentsRepository: Error creating single appointment: $e');
      throw Exception('Failed to create single appointment: $e');
    }
  }

  // Create multiple appointments
  Future<List<AppointmentModel>> createMultipleAppointments({
    required String patientId,
    required List<String> timeSlotIds,
    required List<DateTime> appointmentDates,
    required double totalConsultationFee,
    required double totalPaidAmount,
    String? notes,
  }) async {
    try {
      debugPrint('🔍 AppointmentsRepository: Creating ${timeSlotIds.length} appointments...');

      final groupId = DateTime.now().millisecondsSinceEpoch.toString();
      final remainingAmount = totalConsultationFee - totalPaidAmount;
      final feePerAppointment = totalConsultationFee / timeSlotIds.length;
      final paidPerAppointment = totalPaidAmount / timeSlotIds.length;
      final remainingPerAppointment = remainingAmount / timeSlotIds.length;

      final appointmentsData = <Map<String, dynamic>>[];
      final timeSlotsRepo = TimeSlotsRepository();

      for (int i = 0; i < timeSlotIds.length; i++) {
        // Get employee_id from time slot
        final timeSlot = await timeSlotsRepo.getTimeSlotById(timeSlotIds[i]);
        final employeeId = timeSlot?.employeeId;

        appointmentsData.add({
          'patient_id': patientId,
          'time_slot_id': timeSlotIds[i],
          'employee_id': employeeId,
          'appointment_date': appointmentDates[i].toIso8601String().split('T')[0],
          'status': AppointmentModel.statusConfirmed,
          'appointment_type': 'consultation',
          'duration_minutes': 30,
          'consultation_fee': feePerAppointment,
          'paid_amount': paidPerAppointment,
          'remaining_amount': remainingPerAppointment,
          'is_multiple_booking': true,
          'multiple_booking_group_id': groupId,
          'booking_sequence': i + 1,
          'notes': notes,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }

      final response = await SupabaseConfig.appointments
          .insert(appointmentsData)
          .select();

      debugPrint('✅ AppointmentsRepository: ${response.length} appointments created successfully');

      return (response as List)
          .map((json) => AppointmentModel.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('❌ AppointmentsRepository: Error creating multiple appointments: $e');
      throw Exception('Failed to create multiple appointments: $e');
    }
  }



  // Update appointment payment
  Future<AppointmentModel> updateAppointmentPayment({
    required String appointmentId,
    required double consultationFee,
    required double paidAmount,
  }) async {
    try {
      debugPrint('🔍 AppointmentsRepository: Updating appointment $appointmentId payment');

      final remainingAmount = consultationFee - paidAmount;

      final response = await SupabaseConfig.appointments
          .update({
            'consultation_fee': consultationFee,
            'paid_amount': paidAmount,
            'remaining_amount': remainingAmount,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', appointmentId)
          .select()
          .single();

      debugPrint('✅ AppointmentsRepository: Appointment payment updated successfully');

      return AppointmentModel.fromJson(response);
    } catch (e) {
      debugPrint('❌ AppointmentsRepository: Error updating appointment payment: $e');
      throw Exception('Failed to update appointment payment: $e');
    }
  }

  // Get appointments by patient ID
  Future<List<AppointmentModel>> getAppointmentsByPatient(String patientId) async {
    try {
      debugPrint('🔍 AppointmentsRepository: Getting appointments for patient $patientId');

      final response = await SupabaseConfig.appointments
          .select()
          .eq('patient_id', patientId)
          .order('appointment_date', ascending: false)
          .order('created_at', ascending: false);

      debugPrint('📊 AppointmentsRepository: Found ${response.length} appointments for patient');

      return (response as List)
          .map((json) => AppointmentModel.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('❌ AppointmentsRepository: Error getting appointments by patient: $e');
      throw Exception('Failed to get appointments by patient: $e');
    }
  }
}
