import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

class AppointmentModel extends Equatable {
  final String id;
  final String? patientId;
  final String? employeeId;
  final DateTime appointmentDate;
  final String? appointmentTime;
  final String? timeSlotId; // Keep for backward compatibility
  final String status;
  final String appointmentType;
  final int durationMinutes;
  final String? sessionNotes;
  final DateTime? nextAppointmentDate;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AppointmentModel({
    required this.id,
    this.patientId,
    this.employeeId,
    required this.appointmentDate,
    this.appointmentTime,
    this.timeSlotId,
    this.status = 'available',
    this.appointmentType = 'consultation',
    this.durationMinutes = 30,
    this.sessionNotes,
    this.nextAppointmentDate,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  // Check if appointment is booked
  bool get isBooked =>
      status == 'booked' || status == 'confirmed' || status == 'scheduled';

  // Check if appointment is available
  bool get isAvailable => status == 'available';

  // Check if appointment is completed
  bool get isCompleted => status == 'completed';

  // Check if appointment is cancelled
  bool get isCancelled => status == 'cancelled';

  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    try {
      debugPrint('🔄 AppointmentModel.fromJson: Parsing JSON: $json');

      final id = json['id'] as String;
      final patientId = json['patient_id'] as String?;
      final employeeId = json['employee_id'] as String?;
      final appointmentDate = DateTime.parse(
        json['appointment_date'] as String,
      );
      final appointmentTime = json['appointment_time'] as String?;
      final timeSlotId = json['time_slot_id'] as String?;
      final status = json['status'] as String? ?? 'available';
      final appointmentType = json['appointment_type'] as String? ?? 'consultation';
      final durationMinutes = json['duration_minutes'] as int? ?? 30;
      final sessionNotes = json['session_notes'] as String?;
      final nextAppointmentDate = json['next_appointment_date'] != null
          ? DateTime.parse(json['next_appointment_date'] as String)
          : null;
      final notes = json['notes'] as String?;
      final createdAt = DateTime.parse(json['created_at'] as String);
      final updatedAt = DateTime.parse(json['updated_at'] as String);

      debugPrint(
        '✅ AppointmentModel.fromJson: Successfully parsed appointment $id',
      );

      return AppointmentModel(
        id: id,
        patientId: patientId,
        employeeId: employeeId,
        appointmentDate: appointmentDate,
        appointmentTime: appointmentTime,
        timeSlotId: timeSlotId,
        status: status,
        appointmentType: appointmentType,
        durationMinutes: durationMinutes,
        sessionNotes: sessionNotes,
        nextAppointmentDate: nextAppointmentDate,
        notes: notes,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );
    } catch (e, stackTrace) {
      debugPrint('❌ AppointmentModel.fromJson: Error parsing JSON: $e');
      debugPrint('📍 AppointmentModel.fromJson: JSON was: $json');
      debugPrint('📍 AppointmentModel.fromJson: Stack trace: $stackTrace');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'patient_id': patientId,
      'employee_id': employeeId,
      'appointment_date': appointmentDate.toIso8601String().split('T')[0],
      'appointment_time': appointmentTime,
      'time_slot_id': timeSlotId,
      'status': status,
      'appointment_type': appointmentType,
      'duration_minutes': durationMinutes,
      'session_notes': sessionNotes,
      'next_appointment_date': nextAppointmentDate?.toIso8601String().split('T')[0],
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };

    // فقط أضف id إذا لم يكن فارغاً (للتحديث)
    if (id.isNotEmpty) {
      json['id'] = id;
    }

    debugPrint('🔄 AppointmentModel.toJson: Generated JSON: $json');
    return json;
  }

  AppointmentModel copyWith({
    String? id,
    String? patientId,
    String? employeeId,
    DateTime? appointmentDate,
    String? appointmentTime,
    String? timeSlotId,
    String? status,
    String? appointmentType,
    int? durationMinutes,
    String? sessionNotes,
    DateTime? nextAppointmentDate,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AppointmentModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      employeeId: employeeId ?? this.employeeId,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      appointmentTime: appointmentTime ?? this.appointmentTime,
      timeSlotId: timeSlotId ?? this.timeSlotId,
      status: status ?? this.status,
      appointmentType: appointmentType ?? this.appointmentType,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      sessionNotes: sessionNotes ?? this.sessionNotes,
      nextAppointmentDate: nextAppointmentDate ?? this.nextAppointmentDate,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
    id,
    patientId,
    employeeId,
    appointmentDate,
    appointmentTime,
    timeSlotId,
    status,
    appointmentType,
    durationMinutes,
    sessionNotes,
    nextAppointmentDate,
    notes,
    createdAt,
    updatedAt,
  ];
}
