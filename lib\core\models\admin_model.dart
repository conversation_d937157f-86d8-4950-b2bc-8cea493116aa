import 'package:equatable/equatable.dart';
import 'specialization_model.dart';

class EmployeeModel extends Equatable {
  final String id; // This is the auth.uid from Supabase Authentication
  final String name;
  final String email;
  final String role; // Keep for backward compatibility
  final String employeeType; // 'super_admin', 'admin', 'receptionist', 'specialist'
  final String? specializationId;
  final SpecializationModel? specialization;
  final String? phone;
  final String? address;
  final DateTime? hireDate;
  final double? salary;
  final String? notes;
  final String? profileImageUrl;
  final bool isActive;
  final DateTime? lastLogin;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EmployeeModel({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    required this.employeeType,
    this.specializationId,
    this.specialization,
    this.phone,
    this.address,
    this.hireDate,
    this.salary,
    this.notes,
    this.profileImageUrl,
    this.isActive = true,
    this.lastLogin,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeModel.fromJson(Map<String, dynamic> json) {
    return EmployeeModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      role: json['role'] as String? ?? json['employee_type'] as String? ?? 'admin',
      employeeType: json['employee_type'] as String? ?? json['role'] as String? ?? 'admin',
      specializationId: json['specialization_id'] as String?,
      phone: json['phone'] as String?,
      address: json['address'] as String?,
      hireDate: json['hire_date'] != null
          ? DateTime.parse(json['hire_date'] as String)
          : null,
      salary: json['salary'] != null
          ? (json['salary'] as num).toDouble()
          : null,
      notes: json['notes'] as String?,
      profileImageUrl: json['profile_image_url'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      lastLogin: json['last_login'] != null
          ? DateTime.parse(json['last_login'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'name': name,
      'email': email,
      'role': role,
      'employee_type': employeeType,
      'specialization_id': specializationId,
      'phone': phone,
      'address': address,
      'hire_date': hireDate?.toIso8601String().split('T')[0],
      'salary': salary,
      'notes': notes,
      'profile_image_url': profileImageUrl,
      'is_active': isActive,
      'last_login': lastLogin?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };

    // Only include id if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }

    return json;
  }

  EmployeeModel copyWith({
    String? id,
    String? name,
    String? email,
    String? role,
    String? employeeType,
    String? specializationId,
    SpecializationModel? specialization,
    String? phone,
    String? address,
    DateTime? hireDate,
    double? salary,
    String? notes,
    String? profileImageUrl,
    bool? isActive,
    DateTime? lastLogin,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      employeeType: employeeType ?? this.employeeType,
      specializationId: specializationId ?? this.specializationId,
      specialization: specialization ?? this.specialization,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      hireDate: hireDate ?? this.hireDate,
      salary: salary ?? this.salary,
      notes: notes ?? this.notes,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isActive: isActive ?? this.isActive,
      lastLogin: lastLogin ?? this.lastLogin,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get isSpecialist => employeeType == 'specialist';
  bool get isReceptionist => employeeType == 'receptionist';
  bool get isAdmin => employeeType == 'admin' || employeeType == 'super_admin';
  bool get isSuperAdmin => employeeType == 'super_admin';

  String get displayRole {
    switch (employeeType) {
      case 'super_admin':
        return 'مدير عام';
      case 'admin':
        return 'مدير';
      case 'receptionist':
        return 'موظف استقبال';
      case 'specialist':
        return specialization?.name ?? 'أخصائي';
      default:
        return 'موظف';
    }
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        role,
        employeeType,
        specializationId,
        specialization,
        phone,
        address,
        hireDate,
        salary,
        notes,
        profileImageUrl,
        isActive,
        lastLogin,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'EmployeeModel(id: $id, name: $name, employeeType: $employeeType, specialization: ${specialization?.name})';
  }
}

// Alias for backward compatibility
typedef AdminModel = EmployeeModel;
