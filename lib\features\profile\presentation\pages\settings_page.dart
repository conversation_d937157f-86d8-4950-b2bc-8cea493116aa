import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_printer/flutter_bluetooth_printer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_colors.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  PaperSize _selectedPaperSize = PaperSize.mm80;
  String? _connectedPrinterAddress;
  String? _connectedPrinterName;
  bool _isConnecting = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      final paperSizeIndex = prefs.getInt('paper_size') ?? 1; // Default to 80mm
      _selectedPaperSize = PaperSize.values[paperSizeIndex];
      _connectedPrinterAddress = prefs.getString('printer_address');
      _connectedPrinterName = prefs.getString('printer_name');
    });
  }

  Future<void> _savePaperSize(PaperSize paperSize) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('paper_size', paperSize.index);
    setState(() {
      _selectedPaperSize = paperSize;
    });
  }

  Future<void> _savePrinterInfo(String address, String name) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('printer_address', address);
    await prefs.setString('printer_name', name);
    setState(() {
      _connectedPrinterAddress = address;
      _connectedPrinterName = name;
    });
  }

  Future<void> _connectToPrinter() async {
    setState(() {
      _isConnecting = true;
    });

    try {
      final device = await FlutterBluetoothPrinter.selectDevice(context);
      if (device != null) {
        final deviceName = device.name ?? 'طابعة غير معروفة';
        await _savePrinterInfo(device.address, deviceName);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم الاتصال بالطابعة: $deviceName'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الاتصال بالطابعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isConnecting = false;
      });
    }
  }

  Future<void> _disconnectPrinter() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('printer_address');
    await prefs.remove('printer_name');
    setState(() {
      _connectedPrinterAddress = null;
      _connectedPrinterName = null;
    });
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم قطع الاتصال بالطابعة'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'الإعدادات',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Section
            _buildProfileSection(),

            SizedBox(height: 24.h),

            // Printer Settings Section
            _buildPrinterSettingsSection(),

            SizedBox(height: 24.h),

            // App Settings Section
            _buildAppSettingsSection(),

            SizedBox(height: 24.h),

            // Account Settings Section
            _buildAccountSettingsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildProfileSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          children: [
            // Profile Avatar
            CircleAvatar(
              radius: 40.r,
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
              child: Icon(
                Icons.person,
                size: 40.sp,
                color: AppColors.primary,
              ),
            ),

            SizedBox(height: 16.h),

            // Admin Name
            Text(
              'المدير العام',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),

            SizedBox(height: 4.h),

            Text(
              '<EMAIL>',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),

            SizedBox(height: 16.h),

            // Edit Profile Button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  // TODO: Navigate to edit profile
                },
                icon: Icon(Icons.edit, size: 16.sp),
                label: Text('تعديل الملف الشخصي'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: BorderSide(color: AppColors.primary),
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrinterSettingsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.print,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'إعدادات الطابعة',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),

          // Settings List
          _buildSettingsTile(
            icon: Icons.bluetooth,
            title: 'اتصال الطابعة',
            subtitle: _connectedPrinterName ?? 'غير متصل',
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: _showPrinterConnectionDialog,
          ),

          Divider(height: 1),

          _buildSettingsTile(
            icon: Icons.straighten,
            title: 'مقاس الورق',
            subtitle: _getPaperSizeName(_selectedPaperSize),
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: _showPaperSizeDialog,
          ),
        ],
      ),
    );
  }

  Widget _buildAppSettingsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.settings,
                  color: Colors.blue,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'إعدادات التطبيق',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
          ),

          _buildSettingsTile(
            icon: Icons.info_outline,
            title: 'حول التطبيق',
            subtitle: 'الإصدار 1.0.0',
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              // TODO: Navigate to about page
            },
          ),

          Divider(height: 1),

          _buildSettingsTile(
            icon: Icons.help_outline,
            title: 'المساعدة والدعم',
            subtitle: 'الحصول على المساعدة',
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              // TODO: Navigate to help page
            },
          ),

          Divider(height: 1),

          _buildSettingsTile(
            icon: Icons.language,
            title: 'اللغة',
            subtitle: 'العربية',
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              // TODO: Navigate to language settings
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSettingsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.account_circle,
                  color: Colors.orange,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'إعدادات الحساب',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
          ),

          _buildSettingsTile(
            icon: Icons.lock_outline,
            title: 'تغيير كلمة المرور',
            subtitle: 'تحديث كلمة المرور',
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              // TODO: Navigate to change password
            },
          ),

          Divider(height: 1),

          _buildSettingsTile(
            icon: Icons.security,
            title: 'الصلاحيات',
            subtitle: 'إدارة صلاحيات المستخدمين',
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              // TODO: Navigate to permissions
            },
          ),

          Divider(height: 1),

          _buildSettingsTile(
            icon: Icons.logout,
            title: 'تسجيل الخروج',
            subtitle: 'الخروج من التطبيق',
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showLogoutDialog();
            },
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required Widget trailing,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: isDestructive
              ? Colors.red.withValues(alpha: 0.1)
              : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          icon,
          color: isDestructive ? Colors.red : Colors.grey[700],
          size: 20.sp,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
          color: isDestructive ? Colors.red : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14.sp,
          color: Colors.grey[600],
        ),
      ),
      trailing: trailing,
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
    );
  }

  void _showPrinterConnectionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إعدادات الطابعة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_connectedPrinterName != null) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.green, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text(
                          'متصل',
                          style: TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'الطابعة: $_connectedPrinterName',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),
            ] else ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text(
                          'غير متصل',
                          style: TextStyle(
                            color: Colors.orange,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'لم يتم الاتصال بأي طابعة',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),
            ],
          ],
        ),
        actions: [
          if (_connectedPrinterName != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _disconnectPrinter();
              },
              child: Text('قطع الاتصال'),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
            ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _connectToPrinter();
            },
            child: Text(_connectedPrinterName != null ? 'تغيير الطابعة' : 'اتصال بطابعة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  void _showPaperSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('مقاس الورق'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: PaperSize.values.map((paperSize) {
            return RadioListTile<PaperSize>(
              title: Text(_getPaperSizeName(paperSize)),
              subtitle: Text(_getPaperSizeDescription(paperSize)),
              value: paperSize,
              groupValue: _selectedPaperSize,
              onChanged: (value) {
                if (value != null) {
                  Navigator.of(context).pop();
                  _savePaperSize(value);
                }
              },
              activeColor: AppColors.primary,
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تسجيل الخروج'),
        content: Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement logout
            },
            child: Text('تسجيل الخروج'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrinterConnectionCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.print,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'اتصال الطابعة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            if (_connectedPrinterName != null) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.green, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text(
                          'متصل',
                          style: TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'الطابعة: $_connectedPrinterName',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 12.h),
              
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _disconnectPrinter,
                      icon: Icon(Icons.bluetooth_disabled, size: 16.sp),
                      label: Text('قطع الاتصال'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: BorderSide(color: Colors.red),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isConnecting ? null : _connectToPrinter,
                      icon: _isConnecting 
                          ? SizedBox(
                              width: 16.sp,
                              height: 16.sp,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Icon(Icons.bluetooth_connected, size: 16.sp),
                      label: Text(_isConnecting ? 'جاري الاتصال...' : 'تغيير الطابعة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text(
                          'غير متصل',
                          style: TextStyle(
                            color: Colors.orange,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'لم يتم الاتصال بأي طابعة',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 12.h),
              
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isConnecting ? null : _connectToPrinter,
                  icon: _isConnecting 
                      ? SizedBox(
                          width: 16.sp,
                          height: 16.sp,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Icon(Icons.bluetooth, size: 16.sp),
                  label: Text(_isConnecting ? 'جاري البحث...' : 'اتصال بطابعة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaperSizeCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.straighten,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'مقاس الورق',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            ...PaperSize.values.map((paperSize) {
              return RadioListTile<PaperSize>(
                title: Text(_getPaperSizeName(paperSize)),
                subtitle: Text(_getPaperSizeDescription(paperSize)),
                value: paperSize,
                groupValue: _selectedPaperSize,
                onChanged: (value) {
                  if (value != null) {
                    _savePaperSize(value);
                  }
                },
                activeColor: AppColors.primary,
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneralSettingsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'إعدادات عامة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            ListTile(
              leading: Icon(Icons.info_outline),
              title: Text('حول التطبيق'),
              subtitle: Text('الإصدار 1.0.0'),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to about page
              },
            ),
            
            Divider(),
            
            ListTile(
              leading: Icon(Icons.help_outline),
              title: Text('المساعدة والدعم'),
              subtitle: Text('الحصول على المساعدة'),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to help page
              },
            ),
          ],
        ),
      ),
    );
  }

  String _getPaperSizeName(PaperSize paperSize) {
    return switch (paperSize) {
      PaperSize.mm58 => '58 مم',
      PaperSize.mm80 => '80 مم',
    };
  }

  String _getPaperSizeDescription(PaperSize paperSize) {
    return switch (paperSize) {
      PaperSize.mm58 => 'مناسب للطابعات الصغيرة المحمولة',
      PaperSize.mm80 => 'الحجم الأكثر شيوعاً للطابعات الحرارية',
    };
  }
}
