import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_printer/flutter_bluetooth_printer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_colors.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  PaperSize _selectedPaperSize = PaperSize.mm80;
  String? _connectedPrinterAddress;
  String? _connectedPrinterName;
  bool _isConnecting = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      final paperSizeIndex = prefs.getInt('paper_size') ?? 1; // Default to 80mm
      _selectedPaperSize = PaperSize.values[paperSizeIndex];
      _connectedPrinterAddress = prefs.getString('printer_address');
      _connectedPrinterName = prefs.getString('printer_name');
    });
  }

  Future<void> _savePaperSize(PaperSize paperSize) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('paper_size', paperSize.index);
    setState(() {
      _selectedPaperSize = paperSize;
    });
  }

  Future<void> _savePrinterInfo(String address, String name) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('printer_address', address);
    await prefs.setString('printer_name', name);
    setState(() {
      _connectedPrinterAddress = address;
      _connectedPrinterName = name;
    });
  }

  Future<void> _connectToPrinter() async {
    setState(() {
      _isConnecting = true;
    });

    try {
      final device = await FlutterBluetoothPrinter.selectDevice(context);
      if (device != null) {
        await _savePrinterInfo(device.address, device.name);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم الاتصال بالطابعة: ${device.name}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الاتصال بالطابعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isConnecting = false;
      });
    }
  }

  Future<void> _disconnectPrinter() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('printer_address');
    await prefs.remove('printer_name');
    setState(() {
      _connectedPrinterAddress = null;
      _connectedPrinterName = null;
    });
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم قطع الاتصال بالطابعة'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'الإعدادات',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Printer Settings Section
            _buildSectionHeader('إعدادات الطابعة'),
            SizedBox(height: 16.h),
            
            // Printer Connection Card
            _buildPrinterConnectionCard(),
            
            SizedBox(height: 16.h),
            
            // Paper Size Card
            _buildPaperSizeCard(),
            
            SizedBox(height: 32.h),
            
            // App Settings Section
            _buildSectionHeader('إعدادات التطبيق'),
            SizedBox(height: 16.h),
            
            // General Settings Card
            _buildGeneralSettingsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildPrinterConnectionCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.print,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'اتصال الطابعة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            if (_connectedPrinterName != null) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.green, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text(
                          'متصل',
                          style: TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'الطابعة: $_connectedPrinterName',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 12.h),
              
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _disconnectPrinter,
                      icon: Icon(Icons.bluetooth_disabled, size: 16.sp),
                      label: Text('قطع الاتصال'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: BorderSide(color: Colors.red),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isConnecting ? null : _connectToPrinter,
                      icon: _isConnecting 
                          ? SizedBox(
                              width: 16.sp,
                              height: 16.sp,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Icon(Icons.bluetooth_connected, size: 16.sp),
                      label: Text(_isConnecting ? 'جاري الاتصال...' : 'تغيير الطابعة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text(
                          'غير متصل',
                          style: TextStyle(
                            color: Colors.orange,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'لم يتم الاتصال بأي طابعة',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 12.h),
              
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isConnecting ? null : _connectToPrinter,
                  icon: _isConnecting 
                      ? SizedBox(
                          width: 16.sp,
                          height: 16.sp,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Icon(Icons.bluetooth, size: 16.sp),
                  label: Text(_isConnecting ? 'جاري البحث...' : 'اتصال بطابعة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaperSizeCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.straighten,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'مقاس الورق',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            ...PaperSize.values.map((paperSize) {
              return RadioListTile<PaperSize>(
                title: Text(_getPaperSizeName(paperSize)),
                subtitle: Text(_getPaperSizeDescription(paperSize)),
                value: paperSize,
                groupValue: _selectedPaperSize,
                onChanged: (value) {
                  if (value != null) {
                    _savePaperSize(value);
                  }
                },
                activeColor: AppColors.primary,
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneralSettingsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'إعدادات عامة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            ListTile(
              leading: Icon(Icons.info_outline),
              title: Text('حول التطبيق'),
              subtitle: Text('الإصدار 1.0.0'),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to about page
              },
            ),
            
            Divider(),
            
            ListTile(
              leading: Icon(Icons.help_outline),
              title: Text('المساعدة والدعم'),
              subtitle: Text('الحصول على المساعدة'),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to help page
              },
            ),
          ],
        ),
      ),
    );
  }

  String _getPaperSizeName(PaperSize paperSize) {
    switch (paperSize) {
      case PaperSize.mm58:
        return '58 مم';
      case PaperSize.mm80:
        return '80 مم';
      default:
        return 'غير محدد';
    }
  }

  String _getPaperSizeDescription(PaperSize paperSize) {
    switch (paperSize) {
      case PaperSize.mm58:
        return 'مناسب للطابعات الصغيرة المحمولة';
      case PaperSize.mm80:
        return 'الحجم الأكثر شيوعاً للطابعات الحرارية';
      default:
        return '';
    }
  }
}
