import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/patient_model.dart';
import '../widgets/patient_search_widget.dart';
import '../widgets/single_appointment_widget.dart';
import '../widgets/multiple_appointments_widget.dart';

class CreateAppointmentPage extends StatefulWidget {
  const CreateAppointmentPage({super.key});

  @override
  State<CreateAppointmentPage> createState() => _CreateAppointmentPageState();
}

class _CreateAppointmentPageState extends State<CreateAppointmentPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  PatientModel? _selectedPatient;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
     
      body: SingleChildScrollView(
        child: Column(
        children: [
          // Patient Search Section
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'اختيار المريض',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 12.h),
                PatientSearchWidget(
                  onPatientSelected: (patient) {
                    setState(() {
                      _selectedPatient = patient;
                    });
                  },
                  selectedPatient: _selectedPatient,
                ),
              ],
            ),
          ),

          // Booking Type Tabs
          if (_selectedPatient != null) ...[
            Container(
              color: AppColors.white,
              child: TabBar(
                controller: _tabController,
                labelColor: AppColors.primary,
                unselectedLabelColor: AppColors.textSecondary,
                indicatorColor: AppColors.primary,
                tabs: [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.event, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text(
                          'حجز مرة واحدة',
                          style: TextStyle(fontSize: 14.sp),
                        ),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.event_repeat, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text(
                          'حجز متعدد',
                          style: TextStyle(fontSize: 14.sp),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Tab Content
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.6,
              child: TabBarView(
                controller: _tabController,
                children: [
                  SingleAppointmentWidget(
                    patient: _selectedPatient!,
                    onPatientReset: () {
                      setState(() {
                        _selectedPatient = null;
                      });
                    },
                  ),
                  MultipleAppointmentsWidget(
                    patient: _selectedPatient!,
                    onPatientReset: () {
                      setState(() {
                        _selectedPatient = null;
                      });
                    },
                  ),
                ],
              ),
            ),
          ] else ...[
            // No Patient Selected State
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.5,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.person_search,
                      size: 80.sp,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      'يرجى اختيار المريض أولاً',
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'ابحث عن المريض باستخدام الاسم أو رقم الهاتف أو رقم المريض',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
        ),
      ),
    );
  }
}
