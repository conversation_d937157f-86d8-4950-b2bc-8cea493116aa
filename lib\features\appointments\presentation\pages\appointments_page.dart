import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/holiday_model.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../patients/presentation/pages/medical_record_page.dart';
import '../bloc/appointments_bloc.dart';
import '../bloc/appointments_event.dart';
import '../bloc/appointments_state.dart';
import '../bloc/time_slots_bloc.dart';
import '../bloc/time_slots_event.dart';
import '../bloc/holidays_bloc.dart';
import '../bloc/holidays_event.dart';
import '../bloc/clinic_info_bloc.dart';
import '../bloc/clinic_info_event.dart';
import '../widgets/patient_details_bottom_sheet.dart';
import '../widgets/enhanced_patient_details_dialog.dart';
import 'professional_schedules_page.dart';
import 'holidays_page.dart';
import 'clinic_info_page.dart';
import 'create_appointment_page.dart';
import 'edit_appointment_page.dart';
import 'print_appointment_invoice_page.dart';
import '../../../../core/services/firebase_messaging_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AppointmentsPage extends StatefulWidget {
  final bool isVisible;

  const AppointmentsPage({
    super.key,
    this.isVisible = true, // Always visible by default (first page)
  });

  @override
  State<AppointmentsPage> createState() => _AppointmentsPageState();
}

class _AppointmentsPageState extends State<AppointmentsPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _tabController;
  DateTime selectedDate = DateTime.now();
  HolidayModel? selectedDateHoliday;

  // Track if data has been loaded for each tab
  bool _appointmentsLoaded = false;
  bool _timeSlotsLoaded = false;
  bool _holidaysLoaded = false;
  bool _clinicInfoLoaded = false;

  @override
  bool get wantKeepAlive => true; // Keep state alive

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);

    // Add listener to tab changes - load data only when needed
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        debugPrint('🔄 Tab changed to index: ${_tabController.index}');
        switch (_tabController.index) {
          case 0:
            if (!_appointmentsLoaded) {
              debugPrint('📋 Loading appointments for tab 0 (first time)');
              _loadAppointmentsForDate(selectedDate);
              _appointmentsLoaded = true;
            }
            break;
          case 1:
            if (!_timeSlotsLoaded) {
              debugPrint('⏰ Loading time slots for tab 1 (first time)');
              context.read<TimeSlotsBloc>().add(LoadAllTimeSlots());
              _timeSlotsLoaded = true;
            }
            break;
          case 2:
            if (!_holidaysLoaded) {
              debugPrint('🎉 Loading holidays for tab 2 (first time)');
              context.read<HolidaysBloc>().add(LoadAllHolidays());
              _holidaysLoaded = true;
            }
            break;
          case 3:
            if (!_clinicInfoLoaded) {
              debugPrint('🏥 Loading clinic info for tab 3 (first time)');
              context.read<ClinicInfoBloc>().add(const LoadAllClinicInfo());
              _clinicInfoLoaded = true;
            }
            break;
        }
      }
    });

    // Load only today's appointments when page initializes (first tab)
    _loadAppointmentsForDate(selectedDate);
    _appointmentsLoaded = true;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(AppStrings.appointments),
        backgroundColor: AppColors.white,
        elevation: 0,

        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          tabs: const [
            Tab(text: 'عرض الحجوزات'),
            Tab(text: 'حجز موعد جديد'),
            Tab(text: 'مواعيد الأخصائيين'),
            Tab(text: 'أيام الإجازات'),
            Tab(text: 'معلومات العيادة'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Appointments listener for notifications only
          BlocListener<AppointmentsBloc, AppointmentsState>(
            listener: (context, state) {
              if (state is AppointmentsError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: AppColors.error,
                  ),
                );
              } else if (state is AppointmentCreated) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم إضافة الموعد بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              } else if (state is AppointmentUpdated) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تحديث الموعد بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              } else if (state is AppointmentDeleted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف الموعد بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              }
            },
            child: const SizedBox.shrink(),
          ),

          // TabBarView - always visible regardless of appointments state
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Tab 1: Appointments View
                _buildEnhancedAppointmentsView(),

                // Tab 2: Create New Appointment
                const CreateAppointmentPage(),

                // Tab 3: Professional Schedules Management
                BlocProvider.value(
                  value: context.read<TimeSlotsBloc>(),
                  child: const ProfessionalSchedulesPage(),
                ),

                // Tab 4: Holidays Management
                BlocProvider.value(
                  value: context.read<HolidaysBloc>(),
                  child: const HolidaysPage(),
                ),

                // Tab 5: Clinic Info
                BlocProvider.value(
                  value: context.read<ClinicInfoBloc>(),
                  child: const ClinicInfoPage(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _loadAppointmentsForDate(DateTime date) {
    context.read<AppointmentsBloc>().add(LoadAppointmentsByDate(date: date));
    _checkHolidayForDate(date);
  }

  Future<void> _checkHolidayForDate(DateTime date) async {
    try {
      final dateString = date.toIso8601String().split('T')[0];
      final response =
          await SupabaseConfig.holidays
              .select()
              .eq('holiday_date', dateString)
              .eq('is_active', true)
              .maybeSingle();

      if (mounted) {
        setState(() {
          selectedDateHoliday =
              response != null ? HolidayModel.fromJson(response) : null;
        });
      }
    } catch (e) {
      debugPrint('❌ Error checking holiday for date: $e');
      if (mounted) {
        setState(() {
          selectedDateHoliday = null;
        });
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: AppColors.primary),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
      _loadAppointmentsForDate(selectedDate);
    }
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  String _formatTimeTo12Hour(String? timeString) {
    if (timeString == null || timeString.isEmpty) {
      return 'غير محدد';
    }

    try {
      // Parse time string (assuming format HH:mm or HH:mm:ss)
      final parts = timeString.split(':');
      if (parts.length < 2) return timeString;

      int hour = int.parse(parts[0]);
      int minute = int.parse(parts[1]);

      String period = hour >= 12 ? 'م' : 'ص';

      // Convert to 12-hour format
      if (hour == 0) {
        hour = 12; // 12 AM
      } else if (hour > 12) {
        hour = hour - 12; // PM hours
      }

      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return timeString; // Return original if parsing fails
    }
  }

  void _updateAppointmentStatus(
    String appointmentId,
    String status, [
    String? cancellationReason,
  ]) async {
    // إظهار مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // تحديث حالة الحجز
      context.read<AppointmentsBloc>().add(
        UpdateAppointmentStatus(appointmentId: appointmentId, status: status),
      );

      // البحث عن الحجز لإرسال الإشعار
      final appointmentsState = context.read<AppointmentsBloc>().state;
      AppointmentModel? appointment;

      if (appointmentsState is AppointmentsLoaded) {
        appointment = appointmentsState.appointments.firstWhere(
          (apt) => apt.id == appointmentId,
          orElse: () => throw Exception('Appointment not found'),
        );
      }

      // إرسال إشعار للمريض
      if (appointment != null && appointment.patientId != null) {
        await _sendAppointmentStatusNotification(
          appointment,
          status,
          cancellationReason,
        );
      }

      // إخفاء مؤشر التحميل وإعادة تحميل البيانات
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          Navigator.of(context).pop(); // إخفاء مؤشر التحميل

          // إعادة تحميل البيانات للتاريخ المحدد
          context.read<AppointmentsBloc>().add(
            LoadAppointmentsByDate(date: selectedDate),
          );

          // إظهار رسالة نجاح
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تحديث حالة الحجز وإرسال الإشعار بنجاح'),
              backgroundColor: AppColors.success,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      });
    } catch (e) {
      // إخفاء مؤشر التحميل في حالة الخطأ
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الحجز: ${e.toString()}'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _sendAppointmentStatusNotification(
    AppointmentModel appointment,
    String status,
    String? cancellationReason,
  ) async {
    try {
      String title = '';
      String body = '';

      switch (status) {
        case 'completed':
          title = '✅ تم إكمال الفحص والاستشارة';
          body =
              'تم إكمال الفحص والاستشارة بنجاح. نتمنى لك السلامة والشفاء العاجل.';
          break;
        case 'no_show':
          title = '⚠️ عدم حضور للعيادة';
          body =
              'لم تحضر إلى العيادة في الموعد المحدد. يرجى التواصل معنا لإعادة جدولة موعدك.';
          break;
        case 'cancelled':
          title = '❌ تم إلغاء الموعد';
          if (cancellationReason != null && cancellationReason.isNotEmpty) {
            body = 'تم إلغاء موعدك. السبب: $cancellationReason';
          } else {
            body =
                'تم إلغاء موعدك من قبل العيادة. يرجى التواصل معنا لمزيد من التفاصيل.';
          }
          break;
        default:
          return; // لا نرسل إشعار للحالات الأخرى
      }

      // الحصول على FCM tokens للمريض وإرسال الإشعار
      final fcmTokens = await _getPatientFcmTokens(appointment.patientId!);

      for (final token in fcmTokens) {
        await FirebaseMessagingService.sendNotification(
          fcmToken: token,
          title: title,
          body: body,
          data: {
            'type': 'appointment_status_update',
            'appointment_id': appointment.id,
            'status': status,
            'appointment_date': appointment.appointmentDate.toIso8601String(),
          },
        );
      }
    } catch (e) {
      debugPrint('❌ Error sending appointment status notification: $e');
      // لا نرمي الخطأ لأن تحديث الحجز نجح، فقط الإشعار فشل
    }
  }

  Future<List<String>> _getPatientFcmTokens(String patientId) async {
    try {
      // First get the auth_id for this patient
      final patientResponse =
          await Supabase.instance.client
              .from('patients')
              .select('auth_id')
              .eq('id', patientId)
              .maybeSingle();

      if (patientResponse == null || patientResponse['auth_id'] == null) {
        debugPrint('⚠️ No auth_id found for patient: $patientId');
        return [];
      }

      final authId = patientResponse['auth_id'] as String;
      debugPrint('🔍 Found auth_id for patient $patientId: $authId');

      // Now get FCM tokens using auth_id
      final response = await Supabase.instance.client
          .from('user_fcm_tokens')
          .select('fcm_token')
          .eq('user_id', authId)
          .eq('is_active', true);

      if (response.isEmpty) {
        debugPrint('⚠️ No FCM tokens found for auth_id: $authId');
        return [];
      }

      final tokens =
          (response as List)
              .map((item) => item['fcm_token'] as String)
              .where((token) => token.isNotEmpty)
              .toList();

      debugPrint(
        '📱 Found ${tokens.length} FCM tokens for patient: $patientId (auth_id: $authId)',
      );
      return tokens;
    } catch (e) {
      debugPrint('❌ Error getting FCM tokens for patient $patientId: $e');
      return [];
    }
  }

  Future<void> _showCancellationDialog(String appointmentId) async {
    String? selectedReason;
    final reasons = [
      'ظروف طارئة للطبيب',
      'إغلاق العيادة لظروف خاصة',
      'عطل فني في العيادة',
      'طلب من المريض',
      'تعارض في المواعيد',
      'أخرى',
    ];

    await showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text(
              'سبب إلغاء الموعد',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('يرجى اختيار سبب إلغاء الموعد:'),
                const SizedBox(height: 16),
                ...reasons.map(
                  (reason) => RadioListTile<String>(
                    title: Text(reason),
                    value: reason,
                    groupValue: selectedReason,
                    onChanged: (value) {
                      selectedReason = value;
                      Navigator.of(context).pop();
                      _updateAppointmentStatus(
                        appointmentId,
                        'cancelled',
                        selectedReason,
                      );
                    },
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'booked':
      case 'محجوز':
        return AppColors.primary;
      case 'مجدول':
      case 'scheduled':
        return AppColors.warning;
      case 'مؤكد':
      case 'confirmed':
        return AppColors.primary;
      case 'مكتمل':
      case 'completed':
        return AppColors.success;
      case 'ملغي':
      case 'cancelled':
        return AppColors.error;
      case 'لم يحضر':
      case 'no_show':
        return AppColors.textSecondary;
      case 'available':
      case 'متاح':
        return AppColors.gray400;
      default:
        return AppColors.textSecondary;
    }
  }

  Widget _buildEnhancedAppointmentsView() {
    return BlocBuilder<AppointmentsBloc, AppointmentsState>(
      builder: (context, state) {
        if (state is AppointmentsLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is AppointmentsLoaded) {
          return _buildAppointmentsList(state.appointments);
        }

        if (state is AppointmentsError) {
          return _buildErrorView(state.message);
        }

        return _buildInitialView();
      },
    );
  }

  Widget _buildAppointmentsList(List<AppointmentModel> appointments) {
    return RefreshIndicator(
      onRefresh: () => _onRefreshAppointments(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with stats
            _buildAppointmentsHeader(appointments),
            SizedBox(height: 20.h),

            // Holiday check for selected date
            _buildHolidayCheck(),

            // Appointments list (all appointments with patients)
            () {
              // If it's a holiday, show holiday message instead of empty appointments
              if (selectedDateHoliday != null) {
                return SizedBox(
                  height: MediaQuery.of(context).size.height * 0.3,
                  child: _buildHolidayView(),
                );
              }

              final appointmentsWithPatients =
                  appointments.where((a) => a.patientId != null).toList();
              if (appointmentsWithPatients.isEmpty) {
                return SizedBox(
                  height: MediaQuery.of(context).size.height * 0.4,
                  child: _buildEmptyAppointmentsView(),
                );
              }
              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: appointmentsWithPatients.length,
                itemBuilder: (context, index) {
                  final appointment = appointmentsWithPatients[index];
                  return _buildEnhancedAppointmentCard(appointment);
                },
              );
            }(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentsHeader(List<AppointmentModel> appointments) {
    final bookedCount =
        appointments
            .where((a) => a.patientId != null && a.status == 'booked')
            .length;
    final scheduledCount =
        appointments.where((a) => a.status == 'scheduled').length;
    final completedCount =
        appointments.where((a) => a.status == 'completed').length;
    final cancelledCount =
        appointments.where((a) => a.status == 'cancelled').length;
    final noShowCount = appointments.where((a) => a.status == 'no_show').length;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date header with selector - تصغير الكارت
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isToday(selectedDate) ? 'مواعيد اليوم' : 'المواعيد',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      '${_getDayName(selectedDate.weekday)} - ${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              InkWell(
                onTap: _selectDate,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 10.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6.r),
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 14.w,
                        color: AppColors.primary,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        'تغيير',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // إحصائيات الحجوزات في صف واحد
          Row(
            children: [
              Expanded(
                child: _buildCompactStatCard(
                  'كشف',
                  bookedCount.toString(),
                  AppColors.primary,
                  Icons.medical_services,
                ),
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: _buildCompactStatCard(
                  'استشارة',
                  scheduledCount.toString(),
                  AppColors.warning,
                  Icons.schedule,
                ),
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: _buildCompactStatCard(
                  'مكتملة',
                  completedCount.toString(),
                  AppColors.success,
                  Icons.check_circle,
                ),
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: _buildCompactStatCard(
                  'ملغية',
                  cancelledCount.toString(),
                  AppColors.error,
                  Icons.cancel,
                ),
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: _buildCompactStatCard(
                  'لم يحضر',
                  noShowCount.toString(),
                  AppColors.textSecondary,
                  Icons.person_off,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompactStatCard(
    String title,
    String count,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16.w),
          SizedBox(height: 4.h),
          Text(
            count,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 9.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'الإثنين';
      case 2:
        return 'الثلاثاء';
      case 3:
        return 'الأربعاء';
      case 4:
        return 'الخميس';
      case 5:
        return 'الجمعة';
      case 6:
        return 'السبت';
      case 7:
        return 'الأحد';
      default:
        return 'غير محدد';
    }
  }

  Widget _buildEnhancedAppointmentCard(AppointmentModel appointment) {
    final statusColor = _getStatusColor(appointment.status);
    final statusText = _getStatusText(appointment.status);

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: statusColor.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: FutureBuilder<Map<String, dynamic>>(
          future: _getPatientAndTimeSlotInfo(appointment),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            final data = snapshot.data ?? {};
            final patientName = data['patientName'] ?? 'غير محدد';
            final patientPhone = data['patientPhone'] ?? 'غير محدد';
            final patientId = data['patientId'] ?? '';
            final isPremium = data['isPremium'] ?? false;
            final timeSlotStart = data['timeSlotStart'];
            final timeSlotEnd = data['timeSlotEnd'];

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with patient info and status
                Row(
                  children: [
                    // Patient avatar
                    CircleAvatar(
                      radius: 20.r,
                      backgroundColor:
                          isPremium
                              ? AppColors.primary.withValues(alpha: 0.1)
                              : AppColors.gray200,
                      child: Icon(
                        Icons.person,
                        color:
                            isPremium ? AppColors.primary : AppColors.gray500,
                        size: 20.w,
                      ),
                    ),
                    SizedBox(width: 12.w),

                    // Patient details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Flexible(
                                child: Text(
                                  patientName,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.textPrimary,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (isPremium) ...[
                                SizedBox(width: 6.w),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 6.w,
                                    vertical: 2.h,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.primary,
                                    borderRadius: BorderRadius.circular(4.r),
                                  ),
                                  child: Text(
                                    'مميز',
                                    style: TextStyle(
                                      fontSize: 10.sp,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          SizedBox(height: 2.h),
                          Text(
                            'ID: $patientId',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Status badge
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12.r),
                        border: Border.all(
                          color: statusColor.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        statusText,
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          color: statusColor,
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 12.h),

                // Contact and appointment info
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Phone with actions
                          Row(
                            children: [
                              Icon(
                                Icons.phone,
                                size: 14.w,
                                color: AppColors.textSecondary,
                              ),
                              SizedBox(width: 4.w),
                              Expanded(
                                child: Text(
                                  patientPhone,
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ),
                              if (patientPhone != 'غير محدد') ...[
                                SizedBox(width: 8.w),
                                // Call button
                                InkWell(
                                  onTap: () => _makePhoneCall(patientPhone),
                                  child: Container(
                                    padding: EdgeInsets.all(4.w),
                                    decoration: BoxDecoration(
                                      color: AppColors.primary.withValues(
                                        alpha: 0.1,
                                      ),
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                    child: Icon(
                                      Icons.phone,
                                      size: 14.w,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 4.w),
                                // Copy button
                                InkWell(
                                  onTap: () => _copyToClipboard(patientPhone),
                                  child: Container(
                                    padding: EdgeInsets.all(4.w),
                                    decoration: BoxDecoration(
                                      color: AppColors.primary.withValues(
                                        alpha: 0.1,
                                      ),
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                    child: Icon(
                                      Icons.copy,
                                      size: 14.w,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),

                          SizedBox(height: 6.h),

                          // Time slot
                          if (timeSlotStart != null && timeSlotEnd != null)
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time,
                                  size: 14.w,
                                  color: AppColors.textSecondary,
                                ),
                                SizedBox(width: 4.w),
                                Text(
                                  '${_formatTimeTo12Hour(timeSlotStart)} - ${_formatTimeTo12Hour(timeSlotEnd)}',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),

                          SizedBox(height: 6.h),

                          // Date
                          Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 14.w,
                                color: AppColors.textSecondary,
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                '${appointment.appointmentDate.day}/${appointment.appointmentDate.month}/${appointment.appointmentDate.year}',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // Notes if available
                if (appointment.notes?.isNotEmpty == true) ...[
                  SizedBox(height: 12.h),
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: AppColors.gray100,
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Text(
                      appointment.notes!,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],

                // Action buttons
                if (appointment.patientId != null) ...[
                  SizedBox(height: 16.h),
                  Row(
                    children: [
                      Expanded(
                        child: _PatientPageButton(
                          onPressed: () => _navigateToPatientPage(data),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _showStatusChangeDialog(appointment),
                          icon: Icon(Icons.edit_note, size: 16.w),
                          label: const Text('تغيير الحالة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _editAppointment(appointment),
                          icon: Icon(Icons.edit, size: 16.w, color: AppColors.white),
                          label: Text(
                            'تعديل الموعد',
                            style: TextStyle(color: AppColors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.warning,
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _printAppointmentInvoice(appointment, data),
                          icon: Icon(Icons.print, size: 16.w, color: AppColors.white),
                          label: Text(
                            'طباعة الفاتورة',
                            style: TextStyle(color: AppColors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.success,
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyAppointmentsView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.event_available, size: 64.w, color: AppColors.gray400),
          SizedBox(height: 16.h),
          Text(
            'لا توجد حجوزات',
            style: TextStyle(fontSize: 18.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 8.h),
          Text(
            'لا توجد مواعيد محجوزة في هذا التاريخ',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.w, color: AppColors.error),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل المواعيد',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(fontSize: 14.sp, color: AppColors.error),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed:
                () => context.read<AppointmentsBloc>().add(
                  LoadTodayAppointments(),
                ),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.event, size: 64.w, color: AppColors.gray400),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل المواعيد...',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed:
                () => context.read<AppointmentsBloc>().add(
                  LoadTodayAppointments(),
                ),
            child: const Text('تحميل المواعيد'),
          ),
        ],
      ),
    );
  }

  void _showStatusChangeDialog(AppointmentModel appointment) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تغيير حالة الحجز'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'اختر الحالة الجديدة للحجز:',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 16.h),
                // خيارات الحالة
                _buildStatusOption(
                  appointment,
                  'completed',
                  'مكتمل',
                  Icons.check_circle,
                  AppColors.success,
                ),
                SizedBox(height: 8.h),
                _buildStatusOption(
                  appointment,
                  'cancelled',
                  'ملغي',
                  Icons.cancel,
                  AppColors.error,
                ),
                SizedBox(height: 8.h),
                _buildStatusOption(
                  appointment,
                  'no_show',
                  'لم يحضر',
                  Icons.person_off,
                  AppColors.textSecondary,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  Widget _buildStatusOption(
    AppointmentModel appointment,
    String status,
    String label,
    IconData icon,
    Color color,
  ) {
    final isCurrentStatus = appointment.status == status;

    return InkWell(
      onTap:
          isCurrentStatus
              ? null
              : () {
                Navigator.of(context).pop();
                _updateAppointmentStatusWithConfirmation(
                  appointment,
                  status,
                  label,
                );
              },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color:
              isCurrentStatus ? color.withValues(alpha: 0.1) : AppColors.gray50,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isCurrentStatus ? color : AppColors.gray200,
            width: isCurrentStatus ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isCurrentStatus ? color : AppColors.textSecondary,
              size: 20.w,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight:
                      isCurrentStatus ? FontWeight.bold : FontWeight.normal,
                  color: isCurrentStatus ? color : AppColors.textPrimary,
                ),
              ),
            ),
            if (isCurrentStatus) Icon(Icons.check, color: color, size: 20.w),
          ],
        ),
      ),
    );
  }

  void _updateAppointmentStatusWithConfirmation(
    AppointmentModel appointment,
    String newStatus,
    String statusLabel,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('تأكيد تغيير الحالة'),
            content: Text(
              'هل أنت متأكد من تغيير حالة الحجز إلى "$statusLabel"؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  if (newStatus == 'cancelled') {
                    _showCancellationDialog(appointment.id);
                  } else {
                    _updateAppointmentStatus(appointment.id, newStatus);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: _getStatusColor(newStatus),
                ),
                child: const Text('تأكيد'),
              ),
            ],
          ),
    );
  }

  Future<Map<String, dynamic>> _getPatientAndTimeSlotInfo(
    AppointmentModel appointment,
  ) async {
    try {
      final result = <String, dynamic>{};

      // Get patient info
      if (appointment.patientId != null) {
        final patientResponse =
            await SupabaseConfig.patients
                .select('id, name, phone, is_premium')
                .eq('id', appointment.patientId!)
                .maybeSingle();

        if (patientResponse != null) {
          result['patientName'] = patientResponse['name'] ?? 'غير محدد';
          result['patientPhone'] = patientResponse['phone'] ?? 'غير محدد';
          result['patientId'] = patientResponse['id'] ?? '';
          result['isPremium'] = patientResponse['is_premium'] ?? false;
        }
      }

      // Get time slot info
      if (appointment.timeSlotId != null) {
        final timeSlotResponse =
            await SupabaseConfig.timeSlots
                .select('start_time, end_time')
                .eq('id', appointment.timeSlotId!)
                .maybeSingle();

        if (timeSlotResponse != null) {
          result['timeSlotStart'] = timeSlotResponse['start_time'];
          result['timeSlotEnd'] = timeSlotResponse['end_time'];
        }
      }

      return result;
    } catch (e) {
      debugPrint('❌ Error getting patient/time slot info: $e');
      return {};
    }
  }

  void _makePhoneCall(String phoneNumber) async {
    try {
      final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
      final Uri phoneUri = Uri.parse('tel:$cleanNumber');

      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('❌ Error making phone call: $e');
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ رقم الهاتف'),
          backgroundColor: AppColors.success,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _navigateToPatientPage(Map<String, dynamic> patientData) async {
    final patientId = patientData['patientId'] ?? '';
    final patientName = patientData['patientName'] ?? 'غير محدد';
    final patientPhone = patientData['patientPhone'];

    if (patientId.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => EnhancedPatientDetailsDialog(
        patientId: patientId,
        patientName: patientName,
        patientPhone: patientPhone,
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return AppColors.primary;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      case 'no_show':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'مؤكد';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'no_show':
        return 'لم يحضر';
      default:
        return 'غير محدد';
    }
  }
}

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'booked':
        return 'كشف';
      case 'confirmed':
        return 'مؤكد';
      case 'scheduled':
        return 'استشارة';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'no_show':
        return 'لم يحضر';
      case 'available':
      default:
        return 'متاح';
    }
  }

  Widget _buildHolidayCheck() {
    if (selectedDateHoliday == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(Icons.event_busy, color: AppColors.warning, size: 24.w),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'هذا اليوم عطلة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.warning,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  selectedDateHoliday!.occasionName,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (selectedDateHoliday!.notes?.isNotEmpty == true) ...[
                  SizedBox(height: 4.h),
                  Text(
                    selectedDateHoliday!.notes!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: AppColors.warning,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              selectedDateHoliday!.holidayTypeDisplayName,
              style: TextStyle(
                fontSize: 10.sp,
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _editAppointment(AppointmentModel appointment) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EditAppointmentPage(appointment: appointment),
      ),
    ).then((_) {
      // Refresh appointments after editing
      if (mounted) {
        context.read<AppointmentsBloc>().add(LoadAppointmentsByDate(date: selectedDate));
      }
    });
  }

  void _printAppointmentInvoice(AppointmentModel appointment, Map<String, dynamic> patientData) async {
    PatientModel? patient;

    if (appointment.patientId != null) {
      try {
        // Create patient model from data
        patient = PatientModel(
          id: patientData['patientId'] ?? '',
          name: patientData['patientName'] ?? 'غير محدد',
          phone: patientData['patientPhone'],
          gender: 'male', // Default value
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      } catch (e) {
        debugPrint('Error creating patient model: $e');
      }
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PrintAppointmentInvoicePage(
          appointment: appointment,
          patient: patient,
        ),
      ),
    );
  }

  Widget _buildHolidayView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(50.r),
            ),
            child: Icon(
              Icons.beach_access,
              size: 48.w,
              color: AppColors.warning,
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد مواعيد - يوم عطلة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.warning,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'العيادة مغلقة في هذا اليوم',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Future<void> _onRefreshAppointments() async {
    // تحديث بيانات الحجوزات للتاريخ المحدد
    context.read<AppointmentsBloc>().add(
      LoadAppointmentsByDate(date: selectedDate),
    );
    // تحديث بيانات الإجازة للتاريخ المحدد
    _checkHolidayForDate(selectedDate);
    // انتظار قصير لإظهار مؤشر التحديث
    await Future.delayed(const Duration(milliseconds: 500));
  }
}

// Widget منفصل لزر صفحة المريض مع loading state
class _PatientPageButton extends StatefulWidget {
  final VoidCallback onPressed;

  const _PatientPageButton({required this.onPressed});

  @override
  State<_PatientPageButton> createState() => _PatientPageButtonState();
}

class _PatientPageButtonState extends State<_PatientPageButton> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      onPressed:
          _isLoading
              ? null
              : () async {
                setState(() => _isLoading = true);

                // تأخير قصير لإظهار loading state
                await Future.delayed(const Duration(milliseconds: 100));

                widget.onPressed();

                // إعادة تعيين الحالة بعد فترة قصيرة
                if (mounted) {
                  await Future.delayed(const Duration(milliseconds: 500));
                  setState(() => _isLoading = false);
                }
              },
      icon:
          _isLoading
              ? SizedBox(
                width: 16.w,
                height: 16.w,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              )
              : Icon(Icons.person, size: 16.w),
      label: Text(
        _isLoading ? 'جاري التحميل...' : 'صفحة المريض',
        style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w500),
      ),
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 8.w),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
        side: BorderSide(
          color: _isLoading ? AppColors.gray300 : AppColors.primary,
          width: 1,
        ),
        foregroundColor: _isLoading ? AppColors.gray500 : AppColors.primary,
      ),
    );
  }


}
