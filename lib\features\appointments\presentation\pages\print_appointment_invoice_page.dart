import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_printer/flutter_bluetooth_printer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:typed_data';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/admin_model.dart';
import '../../../../core/utils/time_utils.dart';
import '../../../employees/data/repositories/employees_repository.dart';

class PrintAppointmentInvoicePage extends StatefulWidget {
  final AppointmentModel appointment;
  final PatientModel patient;

  const PrintAppointmentInvoicePage({
    super.key,
    required this.appointment,
    required this.patient,
  });

  @override
  State<PrintAppointmentInvoicePage> createState() => _PrintAppointmentInvoicePageState();
}

class _PrintAppointmentInvoicePageState extends State<PrintAppointmentInvoicePage> {
  ReceiptController? controller;
  PaperSize _paperSize = PaperSize.mm80;
  EmployeeModel? _employee;
  bool _isLoadingEmployee = false;

  @override
  void initState() {
    super.initState();
    _loadPrinterSettings();
    _loadEmployeeData();
  }

  Future<void> _loadPrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final paperSizeIndex = prefs.getInt('paper_size') ?? 1; // Default to 80mm
    setState(() {
      _paperSize = PaperSize.values[paperSizeIndex];
    });
  }

  Future<void> _loadEmployeeData() async {
    debugPrint('🔍 Loading employee data for appointment: ${widget.appointment.id}');
    debugPrint('🔍 Employee ID: ${widget.appointment.employeeId}');

    if (widget.appointment.employeeId == null) {
      debugPrint('❌ No employee ID found in appointment');
      return;
    }

    setState(() {
      _isLoadingEmployee = true;
    });

    try {
      final employees = await EmployeesRepository.getAllEmployees();
      final employee = employees.firstWhere(
        (emp) => emp.id == widget.appointment.employeeId,
        orElse: () => throw Exception('Employee not found'),
      );

      debugPrint('✅ Found employee: ${employee.name}, specialization: ${employee.specialization?.name}');

      if (mounted) {
        setState(() {
          _employee = employee;
          _isLoadingEmployee = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingEmployee = false;
        });
      }
      debugPrint('❌ Error loading employee data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'طباعة فاتورة الحجز',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _printInvoice,
            icon: Icon(Icons.print),
            tooltip: 'طباعة',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Preview Card
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Text(
                    'معاينة فاتورة الحجز',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  _isLoadingEmployee
                      ? SizedBox(
                          height: 200.h,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                                ),
                                SizedBox(height: 16.h),
                                Text(
                                  'جاري تحميل بيانات الأخصائي...',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      : Receipt(
                          builder: (context) => _buildReceiptContent(),
                          onInitialized: (controller) {
                            controller.paperSize = _paperSize;
                            this.controller = controller;
                          },
                        ),
                ],
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Print Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _printInvoice,
                icon: Icon(Icons.print, color: Colors.white),
                label: Text(
                  'طباعة فاتورة الحجز',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReceiptContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Header
        Text(
          'مركز مستشفى إربد الإسلامي',
          style: TextStyle(
            fontSize: 36,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          'للسمع والنطق والسلوك',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16),
        Text(
          'العنوان: إربد - الأردن',
          style: TextStyle(fontSize: 24),
          textAlign: TextAlign.center,
        ),
        
        Divider(thickness: 2),
        
        // Invoice Type
        Text(
          'فاتورة حجز موعد',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),
        
        SizedBox(height: 16),
        Divider(),
        
        // Appointment Info
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('رقم الموعد:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 26)),
            Text(widget.appointment.id.substring(0, 8), style: TextStyle(fontSize: 26)),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('تاريخ الحجز:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 26)),
            Text('${widget.appointment.createdAt.day}/${widget.appointment.createdAt.month}/${widget.appointment.createdAt.year}', style: TextStyle(fontSize: 26)),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('وقت الحجز:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 26)),
            Text('${widget.appointment.createdAt.hour.toString().padLeft(2, '0')}:${widget.appointment.createdAt.minute.toString().padLeft(2, '0')}', style: TextStyle(fontSize: 26)),
          ],
        ),
        
        Divider(),

        // Specialist Info (always show, with fallback)
        Text(
          'بيانات الأخصائي',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الاسم:', style: TextStyle(fontSize: 26)),
            Expanded(
              child: Text(
                _employee?.name != null ? 'د. ${_employee!.name}' : 'غير محدد',
                style: TextStyle(fontSize: 26),
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('التخصص:', style: TextStyle(fontSize: 26)),
            Expanded(
              child: Text(
                _employee?.specialization?.name ?? 'غير محدد',
                style: TextStyle(fontSize: 26),
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),

        Divider(),

        // Patient Info
        Text(
          'بيانات المريض',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الاسم:', style: TextStyle(fontSize: 26)),
            Expanded(
              child: Text(
                widget.patient.name,
                style: TextStyle(fontSize: 26),
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الهاتف:', style: TextStyle(fontSize: 26)),
            Text(widget.patient.phone ?? 'غير محدد', style: TextStyle(fontSize: 26)),
          ],
        ),
        
        Divider(),
        
        // Appointment Details
        Text(
          'تفاصيل الموعد',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
        ),
        SizedBox(height: 16),
        
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('تاريخ الموعد:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 26)),
            Text('${widget.appointment.appointmentDate.day}/${widget.appointment.appointmentDate.month}/${widget.appointment.appointmentDate.year}', style: TextStyle(fontSize: 26)),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('يوم الموعد:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 26)),
            Text(TimeUtils.getArabicDayName(widget.appointment.appointmentDate.weekday), style: TextStyle(fontSize: 26)),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('المدة:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 26)),
            Text('${widget.appointment.durationMinutes} دقيقة', style: TextStyle(fontSize: 26)),
          ],
        ),
        
        SizedBox(height: 16),
        Divider(thickness: 3),

        // Financial Details
        Text(
          'التفاصيل المالية',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
        ),
        SizedBox(height: 16),
        
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('رسوم الحجز:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28)),
            Text('${widget.appointment.consultationFee.toStringAsFixed(2)} د.ا', style: TextStyle(fontSize: 28)),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('المبلغ المدفوع:', style: TextStyle(fontSize: 26, color: Colors.black)),
            Text('${widget.appointment.paidAmount.toStringAsFixed(2)} د.ا', style: TextStyle(fontSize: 26, color: Colors.black)),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('المبلغ المتبقي:', style: TextStyle(color: Colors.black, fontSize: 26)),
            Text('${widget.appointment.remainingAmount.toStringAsFixed(2)} د.ا', style: TextStyle(color: Colors.black, fontSize: 26)),
          ],
        ),

        SizedBox(height: 16),
        Divider(thickness: 3),

        // Status
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('حالة الموعد:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28, color: Colors.black)),
            Text(
              _getStatusText(),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 28,
                color: Colors.black,
              ),
            ),
          ],
        ),

        // Notes if available
        if (widget.appointment.notes != null && widget.appointment.notes!.isNotEmpty) ...[
          SizedBox(height: 16),
          Divider(),
          Text(
            'ملاحظات',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
          ),
          SizedBox(height: 8),
          Text(
            widget.appointment.notes!,
            style: TextStyle(fontSize: 24),
            textAlign: TextAlign.center,
          ),
        ],

        SizedBox(height: 32),

        // Footer
        Text(
          "شكرا لزيارتكم",
          style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),

        // مساحة إضافية في نهاية الفاتورة
        SizedBox(height: 40),
      ],
    );
  }

  String _getStatusText() {
    switch (widget.appointment.status) {
      case 'confirmed':
        return 'مؤكد';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'noShow':
        return 'لم يحضر';
      default:
        return 'غير محدد';
    }
  }

  Future<void> _printInvoice() async {
    // Get printer address from settings
    final prefs = await SharedPreferences.getInstance();
    final printerAddress = prefs.getString('printer_address');

    if (printerAddress == null) {
      _showError('لم يتم تكوين الطابعة. يرجى الذهاب إلى إعدادات الطابعة أولاً');
      return;
    }

    if (controller == null) {
      _showError('خطأ في إعداد الطابعة');
      return;
    }

    // عرض ديالوج جاري الطباعة
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
              SizedBox(height: 16.h),
              Text(
                'جاري الطباعة...',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    try {
      // Print the receipt
      await controller!.print(
        address: printerAddress,
        keepConnected: true,
        addFeeds: 4, // إضافة مساحة إضافية في نهاية الطباعة
      );

      // إرسال أمر قطع الورق (GS V 65 0) لقطع الورق كامل
      await FlutterBluetoothPrinter.printBytes(
        address: printerAddress,
        data: Uint8List.fromList([29, 86, 65, 0]), // أمر قطع الورق (GS V 65 0)
        keepConnected: true,
      );

      // إغلاق ديالوج الطباعة
      if (mounted) {
        Navigator.of(context).pop();
      }

      _showSuccess('تم طباعة فاتورة الحجز بنجاح');

    } catch (e) {
      // إغلاق ديالوج الطباعة في حالة الخطأ
      if (mounted) {
        Navigator.of(context).pop();
      }
      _showError('فشل في طباعة فاتورة الحجز: $e');
    }
  }

  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}