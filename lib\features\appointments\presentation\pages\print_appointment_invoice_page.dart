import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bluetooth_printer/flutter_bluetooth_printer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/models/patient_model.dart';

class PrintAppointmentInvoicePage extends StatefulWidget {
  final AppointmentModel appointment;
  final PatientModel? patient;

  const PrintAppointmentInvoicePage({
    super.key,
    required this.appointment,
    this.patient,
  });

  @override
  State<PrintAppointmentInvoicePage> createState() => _PrintAppointmentInvoicePageState();
}

class _PrintAppointmentInvoicePageState extends State<PrintAppointmentInvoicePage> {
  PaperSize _selectedPaperSize = PaperSize.mm80;
  bool _isConnected = false;
  String? _connectedPrinterName;

  @override
  void initState() {
    super.initState();
    _loadPrinterSettings();
  }

  Future<void> _loadPrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _selectedPaperSize = PaperSize.values.firstWhere(
        (size) => size.name == prefs.getString('paper_size'),
        orElse: () => PaperSize.mm80,
      );
      _connectedPrinterName = prefs.getString('printer_name');
      _isConnected = _connectedPrinterName != null;
    });
  }

  Future<void> _printAppointmentInvoice() async {
    if (!_isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى الاتصال بالطابعة أولاً'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    try {
      final receipt = _generateAppointmentReceipt();
      await FlutterBluetoothPrinter.printReceipt(receipt);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم طباعة فاتورة الحجز بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الطباعة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Map<String, dynamic> _generateAppointmentReceipt() {
    final patient = widget.patient;
    final appointment = widget.appointment;
    
    return {
      'paperSize': _selectedPaperSize.name,
      'data': [
        // Header
        {'type': 'text', 'data': 'مركز مستشفى إربد الإسلامي', 'align': 'center', 'size': 'large', 'style': 'bold'},
        {'type': 'text', 'data': 'للسمع والنطق والسلوك', 'align': 'center', 'size': 'medium'},
        {'type': 'text', 'data': '================================', 'align': 'center'},
        {'type': 'text', 'data': 'فاتورة حجز موعد', 'align': 'center', 'size': 'large', 'style': 'bold'},
        {'type': 'text', 'data': '================================', 'align': 'center'},
        {'type': 'newline'},
        
        // Appointment Info
        {'type': 'text', 'data': 'رقم الحجز: ${appointment.id.substring(0, 8)}', 'align': 'right'},
        {'type': 'text', 'data': 'تاريخ الحجز: ${_formatDate(appointment.appointmentDate)}', 'align': 'right'},
        {'type': 'text', 'data': 'وقت الحجز: ${appointment.appointmentTime ?? "غير محدد"}', 'align': 'right'},
        {'type': 'text', 'data': 'حالة الحجز: ${appointment.statusDisplayName}', 'align': 'right'},
        {'type': 'newline'},
        
        // Patient Info
        if (patient != null) ...[
          {'type': 'text', 'data': 'بيانات المريض:', 'align': 'right', 'style': 'bold'},
          {'type': 'text', 'data': 'الاسم: ${patient!.name}', 'align': 'right'},
          {'type': 'text', 'data': 'رقم المريض: ${patient!.id}', 'align': 'right'},
          if (patient!.phone != null)
            {'type': 'text', 'data': 'الهاتف: ${patient!.phone}', 'align': 'right'},
          {'type': 'newline'},
        ],
        
        // Payment Details
        {'type': 'text', 'data': 'تفاصيل الدفع:', 'align': 'right', 'style': 'bold'},
        {'type': 'text', 'data': '--------------------------------', 'align': 'center'},
        {'type': 'text', 'data': 'سعر الكشف: ${appointment.consultationFee.toStringAsFixed(2)} د.ا', 'align': 'right'},
        {'type': 'text', 'data': 'المبلغ المدفوع: ${appointment.paidAmount.toStringAsFixed(2)} د.ا', 'align': 'right'},
        {'type': 'text', 'data': 'المبلغ المتبقي: ${appointment.remainingAmount.toStringAsFixed(2)} د.ا', 'align': 'right'},
        {'type': 'text', 'data': '================================', 'align': 'center'},
        
        // Multiple Booking Info
        if (appointment.isMultipleBooking) ...[
          {'type': 'newline'},
          {'type': 'text', 'data': 'حجز متعدد:', 'align': 'right', 'style': 'bold'},
          {'type': 'text', 'data': 'رقم المجموعة: ${appointment.multipleBookingGroupId?.substring(0, 8) ?? "غير محدد"}', 'align': 'right'},
          {'type': 'text', 'data': 'ترتيب الحجز: ${appointment.bookingSequence ?? 1}', 'align': 'right'},
        ],
        
        // Notes
        if (appointment.notes != null && appointment.notes!.isNotEmpty) ...[
          {'type': 'newline'},
          {'type': 'text', 'data': 'ملاحظات:', 'align': 'right', 'style': 'bold'},
          {'type': 'text', 'data': appointment.notes!, 'align': 'right'},
        ],
        
        // Footer
        {'type': 'newline'},
        {'type': 'text', 'data': '================================', 'align': 'center'},
        {'type': 'text', 'data': 'تاريخ الطباعة: ${_formatDate(DateTime.now())}', 'align': 'center'},
        {'type': 'text', 'data': 'شكراً لثقتكم بنا', 'align': 'center'},
        {'type': 'text', 'data': '================================', 'align': 'center'},
        {'type': 'newline'},
        {'type': 'newline'},
      ],
    };
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'طباعة فاتورة الحجز',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: IconThemeData(color: AppColors.white),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Preview Card
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Header
                  Text(
                    'مركز مستشفى إربد الإسلامي',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    'للسمع والنطق والسلوك',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  Divider(color: AppColors.primary, thickness: 2),
                  
                  Text(
                    'فاتورة حجز موعد',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  
                  SizedBox(height: 16.h),
                  
                  // Appointment Details
                  _buildDetailRow('رقم الحجز', widget.appointment.id.substring(0, 8)),
                  _buildDetailRow('تاريخ الحجز', _formatDate(widget.appointment.appointmentDate)),
                  _buildDetailRow('وقت الحجز', widget.appointment.appointmentTime ?? "غير محدد"),
                  _buildDetailRow('حالة الحجز', widget.appointment.statusDisplayName),
                  
                  if (widget.patient != null) ...[
                    SizedBox(height: 16.h),
                    Text(
                      'بيانات المريض',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    _buildDetailRow('الاسم', widget.patient!.name),
                    _buildDetailRow('رقم المريض', widget.patient!.id),
                    if (widget.patient!.phone != null)
                      _buildDetailRow('الهاتف', widget.patient!.phone!),
                  ],
                  
                  SizedBox(height: 16.h),
                  
                  // Payment Details
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'تفاصيل الدفع',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                        SizedBox(height: 8.h),
                        _buildDetailRow('سعر الكشف', '${widget.appointment.consultationFee.toStringAsFixed(2)} د.ا'),
                        _buildDetailRow('المبلغ المدفوع', '${widget.appointment.paidAmount.toStringAsFixed(2)} د.ا'),
                        _buildDetailRow('المبلغ المتبقي', '${widget.appointment.remainingAmount.toStringAsFixed(2)} د.ا'),
                      ],
                    ),
                  ),
                  
                  if (widget.appointment.isMultipleBooking) ...[
                    SizedBox(height: 16.h),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Column(
                        children: [
                          Text(
                            'حجز متعدد',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.success,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          _buildDetailRow('رقم المجموعة', widget.appointment.multipleBookingGroupId?.substring(0, 8) ?? "غير محدد"),
                          _buildDetailRow('ترتيب الحجز', '${widget.appointment.bookingSequence ?? 1}'),
                        ],
                      ),
                    ),
                  ],
                  
                  if (widget.appointment.notes != null && widget.appointment.notes!.isNotEmpty) ...[
                    SizedBox(height: 16.h),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: AppColors.warning.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'ملاحظات:',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.warning,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            widget.appointment.notes!,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  
                  SizedBox(height: 16.h),
                  
                  Divider(color: AppColors.primary),
                  
                  Text(
                    'تاريخ الطباعة: ${_formatDate(DateTime.now())}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  
                  Text(
                    'شكراً لثقتكم بنا',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Print Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _printAppointmentInvoice,
                icon: Icon(Icons.print, color: AppColors.white),
                label: Text(
                  _isConnected ? 'طباعة الفاتورة' : 'يرجى الاتصال بالطابعة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isConnected ? AppColors.primary : AppColors.textSecondary,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textPrimary,
            ),
          ),
          Text(
            '$label:',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
