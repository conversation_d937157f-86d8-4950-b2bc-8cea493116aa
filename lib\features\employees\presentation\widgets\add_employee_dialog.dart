import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/admin_model.dart';
import '../../../../core/models/specialization_model.dart';
import '../../../../core/network/supabase_client.dart';
import '../../data/repositories/specializations_repository.dart';
import '../../data/repositories/employees_repository.dart';

class AddEmployeeDialog extends StatefulWidget {
  final EmployeeModel? employee; // null for add, non-null for edit

  const AddEmployeeDialog({super.key, this.employee});

  @override
  State<AddEmployeeDialog> createState() => _AddEmployeeDialogState();
}

class _AddEmployeeDialogState extends State<AddEmployeeDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _salaryController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedEmployeeType = 'admin';
  String? _selectedSpecializationId;
  DateTime? _selectedHireDate;
  List<SpecializationModel> _specializations = [];
  bool _isLoading = false;
  bool _showPasswordField = false;

  @override
  void initState() {
    super.initState();
    _loadSpecializations();
    
    // If editing, populate fields
    if (widget.employee != null) {
      final employee = widget.employee!;
      _nameController.text = employee.name;
      _emailController.text = employee.email;
      // Don't populate password for editing
      _phoneController.text = employee.phone ?? '';
      _addressController.text = employee.address ?? '';
      _salaryController.text = employee.salary?.toString() ?? '';
      _notesController.text = employee.notes ?? '';
      _selectedEmployeeType = employee.employeeType;
      _selectedSpecializationId = employee.specializationId;
      _selectedHireDate = employee.hireDate;
    }
  }

  Future<void> _loadSpecializations() async {
    try {
      final specializations = await SpecializationsRepository.getActiveSpecializations();
      setState(() {
        _specializations = specializations;
      });
    } catch (e) {
      debugPrint('Error loading specializations: $e');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _salaryController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  widget.employee == null ? Icons.person_add : Icons.edit,
                  color: AppColors.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  widget.employee == null ? 'إضافة موظف جديد' : 'تعديل الموظف',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name
                      _buildTextField(
                        controller: _nameController,
                        label: 'الاسم',
                        icon: Icons.person,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال الاسم';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Email
                      _buildTextField(
                        controller: _emailController,
                        label: 'البريد الإلكتروني',
                        icon: Icons.email,
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال البريد الإلكتروني';
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return 'يرجى إدخال بريد إلكتروني صحيح';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Password field
                      if (widget.employee == null || _showPasswordField) ...[
                        _buildTextField(
                          controller: _passwordController,
                          label: widget.employee == null ? 'كلمة المرور' : 'كلمة المرور الجديدة',
                          icon: Icons.lock,
                          obscureText: true,
                          validator: (value) {
                            if (widget.employee == null && (value == null || value.isEmpty)) {
                              return 'يرجى إدخال كلمة المرور';
                            }
                            if (value != null && value.isNotEmpty && value.length < 6) {
                              return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Change password button for existing employees
                      if (widget.employee != null && !_showPasswordField) ...[
                        OutlinedButton.icon(
                          onPressed: () {
                            setState(() {
                              _showPasswordField = true;
                            });
                          },
                          icon: const Icon(Icons.lock_reset),
                          label: const Text('تغيير كلمة المرور'),
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Employee Type
                      _buildDropdown(
                        label: 'نوع الموظف',
                        value: _selectedEmployeeType,
                        items: const [
                          DropdownMenuItem(value: 'super_admin', child: Text('مدير عام')),
                          DropdownMenuItem(value: 'admin', child: Text('مدير')),
                          DropdownMenuItem(value: 'receptionist', child: Text('موظف استقبال')),
                          DropdownMenuItem(value: 'specialist', child: Text('أخصائي')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedEmployeeType = value!;
                            if (value != 'specialist') {
                              _selectedSpecializationId = null;
                            }
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Specialization (only for specialists)
                      if (_selectedEmployeeType == 'specialist') ...[
                        _buildDropdown(
                          label: 'التخصص',
                          value: _selectedSpecializationId,
                          items: _specializations.map((spec) {
                            return DropdownMenuItem(
                              value: spec.id,
                              child: Text(spec.name),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedSpecializationId = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Phone
                      _buildTextField(
                        controller: _phoneController,
                        label: 'رقم الهاتف',
                        icon: Icons.phone,
                        keyboardType: TextInputType.phone,
                      ),
                      const SizedBox(height: 16),

                      // Address
                      _buildTextField(
                        controller: _addressController,
                        label: 'العنوان',
                        icon: Icons.location_on,
                        maxLines: 2,
                      ),
                      const SizedBox(height: 16),

                      // Hire Date
                      _buildDateField(),
                      const SizedBox(height: 16),

                      // Salary
                      _buildTextField(
                        controller: _salaryController,
                        label: 'الراتب',
                        icon: Icons.attach_money,
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 16),

                      // Notes
                      _buildTextField(
                        controller: _notesController,
                        label: 'ملاحظات',
                        icon: Icons.note,
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveEmployee,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : Text(widget.employee == null ? 'إضافة' : 'حفظ'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    bool obscureText = false,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      obscureText: obscureText,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppColors.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primary),
        ),
      ),
    );
  }

  Widget _buildDropdown({
    required String label,
    required String? value,
    required List<DropdownMenuItem<String>> items,
    required void Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      items: items,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primary),
        ),
      ),
    );
  }

  Widget _buildDateField() {
    return InkWell(
      onTap: _selectHireDate,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: 'تاريخ التوظيف',
          prefixIcon: const Icon(Icons.calendar_today, color: AppColors.primary),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.primary),
          ),
        ),
        child: Text(
          _selectedHireDate != null
              ? '${_selectedHireDate!.day}/${_selectedHireDate!.month}/${_selectedHireDate!.year}'
              : 'اختر التاريخ',
          style: TextStyle(
            color: _selectedHireDate != null ? AppColors.textPrimary : AppColors.textSecondary,
          ),
        ),
      ),
    );
  }

  Future<void> _selectHireDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedHireDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _selectedHireDate = date;
      });
    }
  }

  Future<void> _saveEmployee() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedEmployeeType == 'specialist' && _selectedSpecializationId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار التخصص للأخصائي')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      String employeeId = widget.employee?.id ?? '';

      // For new employees, create auth account first
      if (widget.employee == null) {
        // Create user in Supabase Auth
        final authResponse = await SupabaseConfig.client.auth.signUp(
          email: _emailController.text.trim(),
          password: _passwordController.text.trim(),
        );

        if (authResponse.user == null) {
          throw Exception('فشل في إنشاء حساب المصادقة');
        }

        employeeId = authResponse.user!.id;
      }

      final employee = EmployeeModel(
        id: employeeId,
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        role: _selectedEmployeeType,
        employeeType: _selectedEmployeeType,
        specializationId: _selectedSpecializationId,
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
        hireDate: _selectedHireDate,
        salary: _salaryController.text.trim().isEmpty ? null : double.tryParse(_salaryController.text.trim()),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        isActive: widget.employee?.isActive ?? true,
        createdAt: widget.employee?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.employee == null) {
        await EmployeesRepository.addEmployee(employee);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إضافة الموظف بنجاح')),
          );
        }
      } else {
        // Update employee data
        await EmployeesRepository.updateEmployee(employee);

        // Update password if changed
        if (_showPasswordField && _passwordController.text.trim().isNotEmpty) {
          await SupabaseConfig.client.auth.updateUser(
            UserAttributes(password: _passwordController.text.trim()),
          );
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تحديث الموظف بنجاح')),
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
