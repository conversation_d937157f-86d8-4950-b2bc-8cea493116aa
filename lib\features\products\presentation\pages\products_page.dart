import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/product_model.dart';
import '../../../../core/utils/debouncer.dart';
import '../../../../core/widgets/custom_search_bar.dart';
import '../../../../core/widgets/loading_dialog.dart';

import '../../../categories/presentation/pages/categories_page.dart';
import '../../../categories/presentation/bloc/categories_bloc.dart';
import '../bloc/products_bloc.dart';
import '../bloc/products_event.dart';
import '../bloc/products_state.dart';
import 'product_form_page.dart';

class ProductsPage extends StatefulWidget {
  final bool isVisible;
  final bool hasBeenVisited;

  const ProductsPage({
    super.key,
    this.isVisible = false,
    this.hasBeenVisited = false,
  });

  @override
  State<ProductsPage> createState() => _ProductsPageState();
}

class _ProductsPageState extends State<ProductsPage>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();
  final Debouncer _debouncer = Debouncer(milliseconds: 500);
  bool _isDeleteDialogOpen = false;

  // Track if data has been loaded
  bool _dataLoaded = false;

  @override
  bool get wantKeepAlive => true; // Keep state alive

  @override
  void didUpdateWidget(ProductsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if page became visible
    if (widget.isVisible && !oldWidget.isVisible) {
      _loadProductsIfNeeded();
    }
  }

  void _loadProductsIfNeeded() {
    // Only load if page is visible and data hasn't been loaded yet
    if (widget.isVisible && !_dataLoaded) {
      debugPrint('🔄 Loading products data for first time...');
      context.read<ProductsBloc>().add(LoadAllProducts());
      _dataLoaded = true;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debouncer.dispose();
    super.dispose();
  }

  void _showAddProductForm() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => MultiBlocProvider(
              providers: [
                BlocProvider.value(value: context.read<ProductsBloc>()),
                BlocProvider.value(value: context.read<CategoriesBloc>()),
              ],
              child: const ProductFormPage(),
            ),
      ),
    );
  }

  void _showEditProductDialog(ProductModel product) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => MultiBlocProvider(
              providers: [
                BlocProvider.value(value: context.read<ProductsBloc>()),
                BlocProvider.value(value: context.read<CategoriesBloc>()),
              ],
              child: ProductFormPage(product: product, isEditing: true),
            ),
      ),
    );
  }

  void _deleteProduct(String productId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل أنت متأكد من حذف هذا المنتج؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.of(context).pop(); // إغلاق dialog التأكيد

                  // عرض loading indicator
                  setState(() {
                    _isDeleteDialogOpen = true;
                  });

                  LoadingDialog.show(context, 'جاري حذف المنتج...');

                  // تنفيذ الحذف
                  context.read<ProductsBloc>().add(
                    DeleteProduct(productId: productId),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                ),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  Widget _buildProductsView(ProductsLoaded state) {
    return RefreshIndicator(
      onRefresh: _onRefreshProducts,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Search Bar
            CustomSearchBar(
              hintText: 'البحث بالاسم أو الكود...',
              controller: _searchController,
              onSearch: (query) {
                context.read<ProductsBloc>().add(SearchProducts(query: query));
              },
              onClear: () {
                context.read<ProductsBloc>().add(LoadAllProducts());
              },
            ),
            SizedBox(height: 16.h),
            // Products Grid
            Expanded(
              child:
                  state.filteredProducts.isEmpty
                      ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.inventory_outlined,
                              size: 64.w,
                              color: AppColors.gray400,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              state.isSearching
                                  ? 'لا توجد نتائج للبحث'
                                  : 'لا توجد منتجات',
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      )
                      : GridView.builder(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 12.w,
                          mainAxisSpacing: 12.h,
                          childAspectRatio:
                              0.68, // تحديث النسبة لتتناسب مع الأحجام الثابتة
                        ),
                        itemCount: state.filteredProducts.length,
                        itemBuilder: (context, index) {
                          final product = state.filteredProducts[index];
                          return _buildProductCard(product);
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductCard(ProductModel product) {
    return GestureDetector(
      onTap: () => _showEditProductDialog(product),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: AppColors.gray300.withValues(alpha: 0.2),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Product Image Section - Fixed Size
            Container(
              height: 140.h, // ارتفاع ثابت للصور
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.gray100,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.r),
                      topRight: Radius.circular(12.r),
                    ),
                    child: SizedBox(
                      height: 140.h,
                      width: double.infinity,
                      child:
                          product.primaryImageUrl.isNotEmpty
                              ? Image.network(
                                product.primaryImageUrl,
                                fit: BoxFit.cover,
                                width: double.infinity,
                                height: 140.h,
                                errorBuilder: (context, error, stackTrace) {
                                  return _buildImagePlaceholder();
                                },
                                loadingBuilder: (
                                  context,
                                  child,
                                  loadingProgress,
                                ) {
                                  if (loadingProgress == null) return child;
                                  return _buildImagePlaceholder();
                                },
                              )
                              : _buildImagePlaceholder(),
                    ),
                  ),

                  // Discount Badge
                  if (product.hasDiscount)
                    Positioned(
                      top: 8.w,
                      left: 8.w,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.error,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          '${product.discountPercentage.toInt()}% خصم',
                          style: TextStyle(
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w700,
                            color: AppColors.white,
                          ),
                        ),
                      ),
                    ),

                  // Action Buttons
                  Positioned(
                    top: 8.w,
                    right: 8.w,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Edit Button
                        GestureDetector(
                          onTap: () => _showEditProductDialog(product),
                          child: Container(
                            padding: EdgeInsets.all(6.w),
                            decoration: BoxDecoration(
                              color: AppColors.black.withValues(alpha: 0.7),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.edit,
                              size: 16.w,
                              color: AppColors.white,
                            ),
                          ),
                        ),

                        SizedBox(width: 4.w),

                        // Delete Button
                        GestureDetector(
                          onTap: () => _deleteProduct(product.id),
                          child: Container(
                            padding: EdgeInsets.all(6.w),
                            decoration: BoxDecoration(
                              color: AppColors.black.withValues(alpha: 0.7),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.delete,
                              size: 16.w,
                              color: AppColors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Product Info Section - Enhanced Layout
            Container(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Product Name
                  Text(
                    product.name,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  SizedBox(height: 6.h),

                  // Product Code Row
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 6.w,
                          vertical: 2.h,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          product.productCode,
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 4.h), // مساحة صغيرة
                  // Price and Stock Section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Stock Status
                      Flexible(
                        flex: 1,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 6.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color:
                                product.stock > 0
                                    ? AppColors.success.withValues(alpha: 0.1)
                                    : AppColors.error.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          child: Text(
                            product.stock > 0
                                ? 'متوفر (${product.stock})'
                                : 'نفد المخزون',
                            style: TextStyle(
                              fontSize: 9.sp,
                              color:
                                  product.stock > 0
                                      ? AppColors.success
                                      : AppColors.error,
                              fontWeight: FontWeight.w600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),

                      SizedBox(width: 8.w),

                      // Price Section
                      Flexible(
                        flex: 1,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // عرض السعر النهائي (بعد الخصم)
                            Text(
                              '${product.sellingPrice?.toStringAsFixed(0) ?? product.effectivePrice.toStringAsFixed(0)} د.ا',
                              style: TextStyle(
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w700,
                                color: AppColors.primary,
                                height: 1.1,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),

                            // عرض السعر الأصلي إذا كان هناك خصم
                            if (product.hasDiscount && product.originalPrice != null) ...[
                              Text(
                                '${product.originalPrice!.toStringAsFixed(0)} د.ا',
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  color: AppColors.textSecondary,
                                  decoration: TextDecoration.lineThrough,
                                  height: 1.1,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 4.h),

                  // Category Name
                  SizedBox(height: 4.h),
                  Text(
                    product.categoryName,
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onRefreshProducts() async {
    debugPrint('🔄 Refreshing products data...');
    context.read<ProductsBloc>().add(LoadAllProducts());
    await Future.delayed(const Duration(milliseconds: 500));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    // Load data only if page is currently visible
    if (widget.isVisible) {
      _loadProductsIfNeeded();
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(AppStrings.products),
        backgroundColor: AppColors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const CategoriesPage()),
              );
            },
            icon: const Icon(Icons.category),
            tooltip: 'إدارة الفئات',
          ),
        ],
      ),
      body: BlocConsumer<ProductsBloc, ProductsState>(
        listener: (context, state) {
          if (state is ProductsError) {
            // إخفاء loading dialog إذا كان مفتوح
            if (_isDeleteDialogOpen) {
              LoadingDialog.hide(context);
              _isDeleteDialogOpen = false;
            }

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is ProductCreated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة المنتج بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is ProductUpdated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث المنتج بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is ProductDeleted) {
            // إخفاء loading dialog
            if (_isDeleteDialogOpen) {
              LoadingDialog.hide(context);
              _isDeleteDialogOpen = false;
            }

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف المنتج بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is ProductsLoaded) {
            // إخفاء loading dialog عند تحديث القائمة بعد الحذف
            if (_isDeleteDialogOpen) {
              LoadingDialog.hide(context);
              _isDeleteDialogOpen = false;
            }
          }
        },
        builder: (context, state) {
          if (state is ProductsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is ProductsLoaded) {
            return _buildProductsView(state);
          }

          return const Center(child: Text('لا توجد بيانات'));
        },
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: "products_fab",
        onPressed: () => _showAddProductForm(),
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Widget لعرض placeholder عندما لا تتوفر صورة أو فشل تحميلها
  Widget _buildImagePlaceholder() {
    return Container(
      height: 140.h,
      width: double.infinity,
      color: AppColors.gray100,
      child: Center(
        child: Icon(Icons.inventory, size: 40.w, color: AppColors.gray400),
      ),
    );
  }
}
