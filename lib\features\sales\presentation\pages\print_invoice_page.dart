import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_printer/flutter_bluetooth_printer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:typed_data';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/sales_invoice_model.dart';

class PrintInvoicePage extends StatefulWidget {
  final SalesInvoiceModel invoice;

  const PrintInvoicePage({super.key, required this.invoice});

  @override
  State<PrintInvoicePage> createState() => _PrintInvoicePageState();
}

class _PrintInvoicePageState extends State<PrintInvoicePage> {
  ReceiptController? controller;
  PaperSize _paperSize = PaperSize.mm80;

  @override
  void initState() {
    super.initState();
    _loadPrinterSettings();
  }

  Future<void> _loadPrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final paperSizeIndex = prefs.getInt('paper_size') ?? 1; // Default to 80mm
    setState(() {
      _paperSize = PaperSize.values[paperSizeIndex];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'طباعة الفاتورة #${widget.invoice.invoiceNumber}',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _printInvoice,
            icon: Icon(Icons.print),
            tooltip: 'طباعة',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Preview Card
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Text(
                    'معاينة الفاتورة',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Receipt(
                    builder: (context) => _buildReceiptContent(),
                    onInitialized: (controller) {
                      controller.paperSize = _paperSize;
                      this.controller = controller;
                    },
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Print Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _printInvoice,
                icon: Icon(Icons.print, color: Colors.white),
                label: Text(
                  'طباعة الفاتورة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReceiptContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Header
        Text(
          'مركز مستشفى إربد الإسلامي',
          style: TextStyle(
            fontSize: 36,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          'للسمع والنطق والسلوك',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16),
        Text(
          'العنوان: إربد - الأردن',
          style: TextStyle(fontSize: 22),
          textAlign: TextAlign.center,
        ),
        
        Divider(thickness: 2),
        
        // Invoice Info
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('رقم الفاتورة:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24)),
            Text(widget.invoice.invoiceNumber, style: TextStyle(fontSize: 24)),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('التاريخ:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24)),
            Text('${widget.invoice.createdAt.day}/${widget.invoice.createdAt.month}/${widget.invoice.createdAt.year}', style: TextStyle(fontSize: 24)),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الوقت:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24)),
            Text('${widget.invoice.createdAt.hour.toString().padLeft(2, '0')}:${widget.invoice.createdAt.minute.toString().padLeft(2, '0')}', style: TextStyle(fontSize: 24)),
          ],
        ),
        
        Divider(),
        
        // Patient Info
        Text(
          'بيانات المريض',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
        ),
        SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الاسم:', style: TextStyle(fontSize: 24)),
            Expanded(
              child: Text(
                widget.invoice.patient?.name ?? 'غير محدد',
                style: TextStyle(fontSize: 24),
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الهاتف:', style: TextStyle(fontSize: 24)),
            Text(widget.invoice.patient?.phone ?? 'غير محدد', style: TextStyle(fontSize: 24)),
          ],
        ),
        
        Divider(),
        
        // Items
        Text(
          'تفاصيل الفاتورة',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
        ),
        SizedBox(height: 16),

        // Items Header
        Row(
          children: [
            Expanded(flex: 4, child: Text('الخدمة/الكود', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24))),
            Expanded(flex: 1, child: Text('الكمية', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24), textAlign: TextAlign.center)),
            Expanded(flex: 2, child: Text('السعر', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24), textAlign: TextAlign.end)),
            Expanded(flex: 2, child: Text('المجموع', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24), textAlign: TextAlign.end)),
          ],
        ),
        Divider(thickness: 2),

        // Items List
        ...widget.invoice.items.map((item) => Padding(
          padding: EdgeInsets.symmetric(vertical: 4),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 4,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.product?.name ?? item.productName,
                          style: TextStyle(fontSize: 24, fontWeight: FontWeight.w600),
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (item.product?.productCode != null && item.product!.productCode.isNotEmpty) ...[
                          SizedBox(height: 2),
                          Text(
                            'كود: ${item.product!.productCode}',
                            style: TextStyle(fontSize: 20, color: Colors.grey[600]),
                          ),
                        ] else if (item.productCode != null && item.productCode!.isNotEmpty) ...[
                          SizedBox(height: 2),
                          Text(
                            'كود: ${item.productCode}',
                            style: TextStyle(fontSize: 20, color: Colors.grey[600]),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      item.quantity.toString(),
                      style: TextStyle(fontSize: 24),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      item.unitPrice.toStringAsFixed(2),
                      style: TextStyle(fontSize: 24),
                      textAlign: TextAlign.end,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      item.totalPrice.toStringAsFixed(2),
                      style: TextStyle(fontSize: 24),
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              Divider(),
            ],
          ),
        )),

        SizedBox(height: 16),
        Divider(thickness: 3),

        // Totals
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('المجموع الفرعي:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 26)),
            Text('${widget.invoice.totalAmount.toStringAsFixed(2)} د.ا', style: TextStyle(fontSize: 26)),
          ],
        ),
        if (widget.invoice.discountAmount > 0) ...[
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('الخصم:', style: TextStyle(color: Colors.red, fontSize: 26)),
              Text('-${widget.invoice.discountAmount.toStringAsFixed(2)} د.ا', style: TextStyle(color: Colors.red, fontSize: 26)),
            ],
          ),
        ],
        SizedBox(height: 8),
        Divider(thickness: 2),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('المجموع الكلي:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 32)),
            Text('${widget.invoice.finalAmount.toStringAsFixed(2)} د.ا', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 32)),
          ],
        ),

        SizedBox(height: 16),
        Divider(thickness: 3),

        // Payment Status
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('حالة الدفع:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 26)),
            Text(
              _getPaymentStatusText(widget.invoice.paymentStatus),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 26,
                color: _getPaymentStatusColor(widget.invoice.paymentStatus),
              ),
            ),
          ],
        ),

        if (widget.invoice.paymentStatus == PaymentStatus.partial) ...[
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('المبلغ المدفوع:', style: TextStyle(fontSize: 24)),
              Text('${widget.invoice.paidAmount.toStringAsFixed(2)} د.ا', style: TextStyle(fontSize: 24)),
            ],
          ),
          SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('المبلغ المتبقي:', style: TextStyle(color: Colors.red, fontSize: 24)),
              Text('${widget.invoice.remainingAmount.toStringAsFixed(2)} د.ا', style: TextStyle(color: Colors.red, fontSize: 24)),
            ],
          ),
        ],

        SizedBox(height: 32),

        // Footer
        Text(
          'شكراً لزيارتكم',
          style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8),
        Text(
          'نتمنى لكم الشفاء العاجل',
          style: TextStyle(fontSize: 24),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16),
        Text(
          'تم الطباعة في: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year} ${DateTime.now().hour.toString().padLeft(2, '0')}:${DateTime.now().minute.toString().padLeft(2, '0')}',
          style: TextStyle(fontSize: 20),
          textAlign: TextAlign.center,
        ),

        // مساحة إضافية في نهاية الفاتورة
        SizedBox(height: 40),
      ],
    );
  }

  String _getPaymentStatusText(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return 'مدفوع بالكامل';
      case PaymentStatus.partial:
        return 'مدفوع جزئياً';
      case PaymentStatus.returned:
        return 'مرتجع';
    }
  }

  Color _getPaymentStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return Colors.green;
      case PaymentStatus.partial:
        return Colors.orange;
      case PaymentStatus.returned:
        return Colors.red;
    }
  }

  Future<void> _printInvoice() async {
    // Get printer address from settings
    final prefs = await SharedPreferences.getInstance();
    final printerAddress = prefs.getString('printer_address');

    if (printerAddress == null) {
      _showError('لم يتم تكوين الطابعة. يرجى الذهاب إلى إعدادات الطابعة أولاً');
      return;
    }

    if (controller == null) {
      _showError('خطأ في إعداد الطابعة');
      return;
    }

    // عرض ديالوج جاري الطباعة
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
              SizedBox(height: 16.h),
              Text(
                'جاري الطباعة...',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    try {
      // Print the receipt
      await controller!.print(
        address: printerAddress,
        keepConnected: true,
        addFeeds: 4, // إضافة مساحة إضافية في نهاية الطباعة
      );

      // إرسال أمر قطع الورق (GS V 65 0) لقطع الورق كامل
      await FlutterBluetoothPrinter.printBytes(
        address: printerAddress,
        data: Uint8List.fromList([29, 86, 65, 0]), // أمر قطع الورق (GS V 65 0)
        keepConnected: true,
      );

      // إغلاق ديالوج الطباعة
      if (mounted) {
        Navigator.of(context).pop();
      }

      _showSuccess('تم طباعة الفاتورة بنجاح');

    } catch (e) {
      // إغلاق ديالوج الطباعة في حالة الخطأ
      if (mounted) {
        Navigator.of(context).pop();
      }
      _showError('فشل في طباعة الفاتورة: $e');
    }
  }

  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}
