import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_printer/flutter_bluetooth_printer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/sales_invoice_model.dart';

class PrintInvoicePage extends StatefulWidget {
  final SalesInvoiceModel invoice;

  const PrintInvoicePage({super.key, required this.invoice});

  @override
  State<PrintInvoicePage> createState() => _PrintInvoicePageState();
}

class _PrintInvoicePageState extends State<PrintInvoicePage> {
  ReceiptController? controller;
  PaperSize _paperSize = PaperSize.mm80;

  @override
  void initState() {
    super.initState();
    _loadPrinterSettings();
  }

  Future<void> _loadPrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final paperSizeIndex = prefs.getInt('paper_size') ?? 1; // Default to 80mm
    setState(() {
      _paperSize = PaperSize.values[paperSizeIndex];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'طباعة الفاتورة #${widget.invoice.invoiceNumber}',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _printInvoice,
            icon: Icon(Icons.print),
            tooltip: 'طباعة',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Preview Card
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Text(
                    'معاينة الفاتورة',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Receipt(
                    builder: (context) => _buildReceiptContent(),
                    onInitialized: (controller) {
                      controller.paperSize = _paperSize;
                      this.controller = controller;
                    },
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Print Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _printInvoice,
                icon: Icon(Icons.print, color: Colors.white),
                label: Text(
                  'طباعة الفاتورة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReceiptContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Header
        Text(
          'مركز الشفاء الطبي',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          'IIHC Medical Center',
          style: TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8),
        Text(
          'العنوان: شارع الملك فهد، الرياض',
          style: TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
        Text(
          'هاتف: **********',
          style: TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
        
        Divider(thickness: 2),
        
        // Invoice Info
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('رقم الفاتورة:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text(widget.invoice.invoiceNumber),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('التاريخ:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('${widget.invoice.createdAt.day}/${widget.invoice.createdAt.month}/${widget.invoice.createdAt.year}'),
          ],
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الوقت:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('${widget.invoice.createdAt.hour.toString().padLeft(2, '0')}:${widget.invoice.createdAt.minute.toString().padLeft(2, '0')}'),
          ],
        ),
        
        Divider(),
        
        // Patient Info
        Text(
          'بيانات المريض',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الاسم:'),
            Expanded(
              child: Text(
                widget.invoice.patient?.name ?? 'غير محدد',
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: 2),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('الهاتف:'),
            Text(widget.invoice.patient?.phone ?? 'غير محدد'),
          ],
        ),
        
        Divider(),
        
        // Items
        Text(
          'تفاصيل الفاتورة',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
        SizedBox(height: 8),
        
        // Items Header
        Row(
          children: [
            Expanded(flex: 3, child: Text('الخدمة', style: TextStyle(fontWeight: FontWeight.bold))),
            Expanded(flex: 1, child: Text('الكمية', style: TextStyle(fontWeight: FontWeight.bold), textAlign: TextAlign.center)),
            Expanded(flex: 2, child: Text('السعر', style: TextStyle(fontWeight: FontWeight.bold), textAlign: TextAlign.end)),
            Expanded(flex: 2, child: Text('المجموع', style: TextStyle(fontWeight: FontWeight.bold), textAlign: TextAlign.end)),
          ],
        ),
        Divider(),
        
        // Items List
        ...widget.invoice.items.map((item) => Padding(
          padding: EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  item.product?.name ?? 'خدمة',
                  style: TextStyle(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  item.quantity.toString(),
                  style: TextStyle(fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  '${item.unitPrice.toStringAsFixed(2)}',
                  style: TextStyle(fontSize: 12),
                  textAlign: TextAlign.end,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  '${item.totalPrice.toStringAsFixed(2)}',
                  style: TextStyle(fontSize: 12),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        )).toList(),
        
        Divider(),
        
        // Totals
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('المجموع الفرعي:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('${widget.invoice.totalAmount.toStringAsFixed(2)} د.ا'),
          ],
        ),
        if (widget.invoice.discountAmount > 0) ...[
          SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('الخصم:', style: TextStyle(color: Colors.red)),
              Text('-${widget.invoice.discountAmount.toStringAsFixed(2)} د.ا', style: TextStyle(color: Colors.red)),
            ],
          ),
        ],
        SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('المجموع الكلي:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            Text('${widget.invoice.finalAmount.toStringAsFixed(2)} د.ا', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
          ],
        ),
        
        Divider(thickness: 2),
        
        // Payment Status
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('حالة الدفع:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text(
              _getPaymentStatusText(widget.invoice.paymentStatus),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _getPaymentStatusColor(widget.invoice.paymentStatus),
              ),
            ),
          ],
        ),
        
        if (widget.invoice.paymentStatus == PaymentStatus.partial) ...[
          SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('المبلغ المدفوع:'),
              Text('${widget.invoice.paidAmount.toStringAsFixed(2)} د.ا'),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('المبلغ المتبقي:', style: TextStyle(color: Colors.red)),
              Text('${widget.invoice.remainingAmount.toStringAsFixed(2)} د.ا', style: TextStyle(color: Colors.red)),
            ],
          ),
        ],
        
        SizedBox(height: 16),
        
        // Footer
        Text(
          'شكراً لزيارتكم',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        Text(
          'نتمنى لكم الشفاء العاجل',
          style: TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8),
        Text(
          'تم الطباعة في: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year} ${DateTime.now().hour.toString().padLeft(2, '0')}:${DateTime.now().minute.toString().padLeft(2, '0')}',
          style: TextStyle(fontSize: 10),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _getPaymentStatusText(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return 'مدفوع بالكامل';
      case PaymentStatus.partial:
        return 'مدفوع جزئياً';
      case PaymentStatus.returned:
        return 'مرتجع';
    }
  }

  Color _getPaymentStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return Colors.green;
      case PaymentStatus.partial:
        return Colors.orange;
      case PaymentStatus.returned:
        return Colors.red;
    }
  }

  Future<void> _printInvoice() async {
    try {
      final address = await FlutterBluetoothPrinter.selectDevice(context);
      if (address != null) {
        await controller?.print(
          address: address.address,
          keepConnected: true,
          addFeeds: 4,
        );
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم طباعة الفاتورة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الطباعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
