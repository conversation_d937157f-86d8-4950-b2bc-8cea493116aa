-- Update patients table to support new treatment types system
-- Remove height and weight columns, add treatment_types array

-- Remove height and weight columns if they exist
ALTER TABLE patients DROP COLUMN IF EXISTS height;
ALTER TABLE patients DROP COLUMN IF EXISTS weight;

-- Add treatment_types column as TEXT array
ALTER TABLE patients ADD COLUMN IF NOT EXISTS treatment_types TEXT[] DEFAULT '{}';

-- Check the updated structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'patients'
ORDER BY ordinal_position;

-- Show current patients count
SELECT COUNT(*) as total_patients FROM patients;
