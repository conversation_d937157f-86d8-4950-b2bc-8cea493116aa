import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iihc_admin/core/constants/app_colors.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/utils/time_utils.dart';

class TimeSlotSelectionDialog extends StatefulWidget {
  final List<TimeSlotModel> availableTimeSlots;
  final TimeSlotModel? selectedTimeSlot;
  final DateTime selectedDate;

  const TimeSlotSelectionDialog({
    super.key,
    required this.availableTimeSlots,
    this.selectedTimeSlot,
    required this.selectedDate,
  });

  @override
  State<TimeSlotSelectionDialog> createState() => _TimeSlotSelectionDialogState();
}

class _TimeSlotSelectionDialogState extends State<TimeSlotSelectionDialog> {
  TimeSlotModel? _selectedTimeSlot;

  @override
  void initState() {
    super.initState();
    _selectedTimeSlot = widget.selectedTimeSlot;
  }

  @override
  Widget build(BuildContext context) {
    final dayName = TimeUtils.getArabicDayName(widget.selectedDate.weekday);
    final dateStr = '${widget.selectedDate.day}/${widget.selectedDate.month}/${widget.selectedDate.year}';

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  topRight: Radius.circular(16.r),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.access_time,
                    color: AppColors.white,
                    size: 24.sp,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'اختر الوقت المناسب',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.white,
                          ),
                        ),
                        Text(
                          '$dayName - $dateStr',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: AppColors.white,
                      size: 24.sp,
                    ),
                  ),
                ],
              ),
            ),

            // Available time slots count
            Container(
              padding: EdgeInsets.all(16.w),
              child: Row(
                children: [
                  Icon(
                    Icons.schedule,
                    color: AppColors.success,
                    size: 20.sp,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    '${widget.availableTimeSlots.length} موعد متاح',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.success,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            // Time slots list
            Flexible(
              child: widget.availableTimeSlots.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      shrinkWrap: true,
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      itemCount: widget.availableTimeSlots.length,
                      itemBuilder: (context, index) {
                        final timeSlot = widget.availableTimeSlots[index];
                        final isSelected = _selectedTimeSlot?.id == timeSlot.id;
                        
                        return Container(
                          margin: EdgeInsets.only(bottom: 12.h),
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                _selectedTimeSlot = timeSlot;
                              });
                            },
                            borderRadius: BorderRadius.circular(12.r),
                            child: Container(
                              padding: EdgeInsets.all(16.w),
                              decoration: BoxDecoration(
                                color: isSelected 
                                    ? AppColors.primary.withValues(alpha: 0.1)
                                    : AppColors.white,
                                border: Border.all(
                                  color: isSelected 
                                      ? AppColors.primary 
                                      : AppColors.primary.withValues(alpha: 0.2),
                                  width: isSelected ? 2 : 1,
                                ),
                                borderRadius: BorderRadius.circular(12.r),
                                boxShadow: isSelected ? [
                                  BoxShadow(
                                    color: AppColors.primary.withValues(alpha: 0.2),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ] : [
                                  BoxShadow(
                                    color: Colors.grey.withValues(alpha: 0.1),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                children: [
                                  // Time icon
                                  Container(
                                    padding: EdgeInsets.all(10.w),
                                    decoration: BoxDecoration(
                                      color: isSelected 
                                          ? AppColors.primary 
                                          : AppColors.primary.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(10.r),
                                    ),
                                    child: Icon(
                                      Icons.schedule,
                                      color: isSelected 
                                          ? AppColors.white 
                                          : AppColors.primary,
                                      size: 20.sp,
                                    ),
                                  ),
                                  SizedBox(width: 16.w),
                                  
                                  // Time and doctor info
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        // Time in 12-hour format
                                        Text(
                                          TimeUtils.formatTimeRange(timeSlot.startTime, timeSlot.endTime),
                                          style: TextStyle(
                                            fontSize: 18.sp,
                                            fontWeight: FontWeight.bold,
                                            color: isSelected 
                                                ? AppColors.primary 
                                                : AppColors.textPrimary,
                                          ),
                                        ),
                                        SizedBox(height: 4.h),
                                        
                                        // Doctor name
                                        if (timeSlot.employeeName != null)
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.person,
                                                size: 16.sp,
                                                color: AppColors.textSecondary,
                                              ),
                                              SizedBox(width: 4.w),
                                              Text(
                                                'د. ${timeSlot.employeeName}',
                                                style: TextStyle(
                                                  fontSize: 14.sp,
                                                  fontWeight: FontWeight.w600,
                                                  color: AppColors.textPrimary,
                                                ),
                                              ),
                                            ],
                                          ),
                                        
                                        // Specialization
                                        if (timeSlot.specialization != null)
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.medical_services,
                                                size: 16.sp,
                                                color: AppColors.success,
                                              ),
                                              SizedBox(width: 4.w),
                                              Text(
                                                timeSlot.specialization!,
                                                style: TextStyle(
                                                  fontSize: 13.sp,
                                                  color: AppColors.success,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                      ],
                                    ),
                                  ),
                                  
                                  // Selection indicator
                                  if (isSelected)
                                    Container(
                                      padding: EdgeInsets.all(6.w),
                                      decoration: BoxDecoration(
                                        color: AppColors.primary,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.check,
                                        color: AppColors.white,
                                        size: 16.sp,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),

            // Action buttons
            Container(
              padding: EdgeInsets.all(20.w),
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                          side: BorderSide(color: AppColors.textSecondary),
                        ),
                      ),
                      child: Text(
                        'إلغاء',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _selectedTimeSlot != null
                          ? () => Navigator.of(context).pop(_selectedTimeSlot)
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'تأكيد الاختيار',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(40.w),
      child: Column(
        children: [
          Icon(
            Icons.event_busy,
            size: 64.sp,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد مواعيد متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'يرجى اختيار تاريخ آخر',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
